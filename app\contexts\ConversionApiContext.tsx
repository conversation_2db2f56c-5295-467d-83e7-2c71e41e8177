// Facebook Conversion API Context
// Provides centralized conversion API functionality across the app

import React, { createContext, useContext, useCallback, useEffect, useRef } from "react";
import { useLocation } from "@remix-run/react";
import {
  FacebookEventName,
  ViewContentEventData,
  AddToCartEventData,
  InitiateCheckoutEventData,
  PurchaseEventData,
} from "~/types/capi-fb";
import { logEvent, logDebug, logError } from "~/utils/capi-logger";

/**
 * Configuration for the Conversion API context
 */
interface ConversionApiConfig {
  enabled: boolean;
  userData?: any;
  debug?: boolean;
  autoPageTracking?: boolean;
}

/**
 * Event data for tracking
 */
interface EventTrackingData {
  eventName: FacebookEventName;
  eventData?: any;
  eventId?: string;
  eventTime?: number;
}

/**
 * Context value interface
 */
interface ConversionApiContextValue {
  // Configuration
  isEnabled: boolean;

  // Event tracking methods
  trackViewContent: ({ eventData, eventId, eventTime }: { eventData?: ViewContentEventData, eventId?: string, eventTime?: number }) => Promise<void>;
  trackAddToCart: ({ eventData, eventId, eventTime }: { eventData?: AddToCartEventData, eventId?: string, eventTime?: number }) => Promise<void>;
  trackInitiateCheckout: ({ eventData, eventId, eventTime }: { eventData?: InitiateCheckoutEventData, eventId?: string, eventTime?: number }) => Promise<void>;
  trackPurchase: ({ eventData, eventId, eventTime }: { eventData: PurchaseEventData, eventId?: string, eventTime?: number }) => Promise<void>;
  trackCustomEvent: ({ eventName, eventData, eventId, eventTime }: { eventName: FacebookEventName, eventData?: any, eventId?: string, eventTime?: number }) => Promise<void>;

  // Generic tracking method
  trackEvent: (event: EventTrackingData) => Promise<void>;
}

/**
 * Create the context
 */
const ConversionApiContext = createContext<ConversionApiContextValue | null>(null);



interface ConversionApiProviderProps {
  children: React.ReactNode;
  config: ConversionApiConfig;
}

export const ConversionApiProvider: React.FC<ConversionApiProviderProps> = ({
  children,
  config,
}) => {
  const location = useLocation();
  const eventQueueRef = useRef<EventTrackingData[]>([]);
  const isProcessingRef = useRef(false);
  const processQueueTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useE

  /**
   * Send events to the server
   */
  const sendEventsToServer = useCallback(async (events: EventTrackingData[]) => {
    if (!config.enabled || events.length === 0) return;

    try {
      const response = await fetch("/api/capi-fb", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ events }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();
      const eventNames = events.map(e => e.eventName).join(", ");

      logEvent("sent", `${events.length} events`, undefined, {
        eventNames,
        eventsProcessed: result.eventsProcessed,
        success: result.success,
      });

      if (config.debug) {
        logDebug("Events sent successfully", {
          eventCount: events.length,
          eventNames,
          result,
        });
      }

    } catch (error) {
      const eventNames = events.map(e => e.eventName).join(", ");
      logError("Failed to send events to server", error as Error, {
        eventCount: events.length,
        eventNames,
      });

      // Mark events as failed for potential retry
      events.forEach(event => {
        logEvent("failed", event.eventName, event.eventId, {
          error: (error as Error).message,
        });
      });
    }
  }, [config.enabled, config.debug]);

  /**
   * Process event queue
   */
  const processEventQueue = useCallback(async () => {
    if (isProcessingRef.current || eventQueueRef.current.length === 0) return;

    isProcessingRef.current = true;

    try {
      const eventsToProcess = [...eventQueueRef.current];
      eventQueueRef.current = []; // Clear queue immediately to prevent duplicates

      if (config.debug) {
        logDebug("Processing event queue", {
          eventCount: eventsToProcess.length,
          eventNames: eventsToProcess.map(e => e.eventName),
        });
      }

      await sendEventsToServer(eventsToProcess);
    } catch (error) {
      logError("Error processing event queue", error as Error);
    } finally {
      isProcessingRef.current = false;
    }
  }, [sendEventsToServer, config.debug]);

  /**
   * Generic track event method
   */
  const trackEvent = useCallback(async (event: EventTrackingData) => {
    if (!config.enabled) {
      if (config.debug) {
        logDebug("Conversion API disabled, skipping event", { event });
      }
      return;
    }

    // Add to queue
    eventQueueRef.current.push({
      ...event,
      eventData: { ...event.eventData, ...config.userData },
      eventTime: event.eventTime || Date.now(),
    });

    // Process queue
    await processEventQueue();
  }, [config.enabled, config.debug, processEventQueue]);

  /**
   * Track ViewContent event
   */
  const trackViewContent = useCallback(async ({ eventData, eventId, eventTime }: { eventData?: ViewContentEventData, eventId?: string, eventTime?: number }) => {
    await trackEvent({
      eventName: "ViewContent",
      eventData: eventData,
      eventId,
      eventTime,
    });
  }, [trackEvent]);

  /**
   * Track AddToCart event
   */
  const trackAddToCart = useCallback(async ({ eventData, eventId, eventTime }: { eventData?: AddToCartEventData, eventId?: string, eventTime?: number }) => {
    await trackEvent({
      eventName: "AddToCart",
      eventData: eventData,
      eventId,
      eventTime,
    });
  }, [trackEvent]);

  /**
   * Track InitiateCheckout event
   */
  const trackInitiateCheckout = useCallback(async ({ eventData, eventId, eventTime }: { eventData?: InitiateCheckoutEventData, eventId?: string, eventTime?: number }) => {
    await trackEvent({
      eventName: "InitiateCheckout",
      eventData: eventData,
      eventId,
      eventTime,
    });
  }, [trackEvent]);

  /**
   * Track Purchase event
   */
  const trackPurchase = useCallback(async ({ eventData, eventId, eventTime }: { eventData: PurchaseEventData, eventId?: string, eventTime?: number }) => {
    await trackEvent({
      eventName: "Purchase",
      eventData: eventData,
      eventId,
      eventTime,
    });
  }, [trackEvent]);

  /**
   * Track custom event
   */
  const trackCustomEvent = useCallback(async ({ eventName, eventData, eventId, eventTime }: { eventName: FacebookEventName, eventData?: any, eventId?: string, eventTime?: number }) => {
    await trackEvent({
      eventName,
      eventData,
      eventId,
      eventTime,
    });
  }, [trackEvent]);


  useEffect(() => {
    if (config.autoPageTracking && config.enabled) {
      
      const currentPath = location.pathname + location.search;
      
      console.log("track view content:", currentPath)
      trackViewContent({
        eventData: {
          contentType: "page",
          contentName: currentPath,
        },
        eventId: `EVT-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
        eventTime: Date.now(),
      });
    }
  }, [location.key, config.autoPageTracking, config.enabled, trackViewContent]);

  const contextValue: ConversionApiContextValue = {
    isEnabled: config.enabled,
    trackViewContent,
    trackAddToCart,
    trackInitiateCheckout,
    trackPurchase,
    trackCustomEvent,
    trackEvent,
  };

  return (
    <ConversionApiContext.Provider value={contextValue}>
      {children}
    </ConversionApiContext.Provider>
  );
};

/**
 * Hook to use the Conversion API context
 */
export const useConversionApi = (): ConversionApiContextValue => {
  const context = useContext(ConversionApiContext);
  if (!context) {
    throw new Error("useConversionApi must be used within a ConversionApiProvider");
  }
  return context;
};

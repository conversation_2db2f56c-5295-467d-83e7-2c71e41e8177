import {
  <PERSON><PERSON>,
  <PERSON>a,
  <PERSON>let,
  <PERSON><PERSON><PERSON>,
  ScrollRestoration,
  useLocation,
  useNavigate,
  useRouteLoaderData,
  json,
  redirect
} from "@remix-run/react";
import type { LinksFunction, LoaderFunction } from "@remix-run/node";

import "./tailwind.css";
import "./styles/global.css";

import ErrorBoundary from "~/components/ErrorBoundary";
import { useEffect, Suspense } from "react";
import { setItem } from "./utils/localStorage";
import { NetworkConfig } from "./types";
import GlobalSpinnerLoader from "./components/loader/GlobalSpinnerLoader";
import { UserProvider } from "./contexts/userContext";
import { getSession } from "./utils/session.server";
import { parseJWT } from "./utils/token-utils";
import { DecodedToken } from "./types/user";
import { useForceUpdateCheck } from "./contexts/useForceUpdateCheck";
import ForceUpdate from "./components/ForceUpdate";
import PullToRefreshWrapper from "./components/PullToRefresh";
import { useAppConfigStore } from "./stores/appConfig.store";
import { getNetworkConfig } from "./services/auth.server";
import WhatsappCTA from "./components/WhatsappCTA";
import { handleWhatsAppAutoLogin } from "~/utils/auth.server";
import LoginBottomSheet from "./components/LoginBottomSheet";
import { useRequireAuth } from "./hooks/useRequireAuth";
import { requireAuth } from "./utils/clientReponse";
import { NetworkAsset } from "./components/NetworkAssests";
import InstallPWABottomSheet from "./components/InstallPWABottomSheet";
import { userStore } from "./stores/user.store";
import { useFacebookPixel } from "./hooks/useFacebookPixel";
import { useHeapAnalytics } from "./hooks/useHeapAnalytics";
import { useMicrosoftClarity } from "./hooks/useMicrosoftClarity";
import { Toaster } from "./components/ui/toast";
import { useCurrentLocation } from "./hooks/useCurrentLocation";
import { extractCLIDFromUrl, setUpCLIDData } from "./utils/capi-clid";
import { useFacebookPageTracking } from "./hooks/useFacebookConversionApi";

export const links: LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous"
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap"
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap"
  },
  // Add this line for the manifest
  { rel: "manifest", href: "/manifest" }
];

interface LoaderData {
  minVersion: number;
  networkConfig: NetworkConfig;
  decode?: DecodedToken;
}

export const loader: LoaderFunction = async ({ request }) => {
  // Try WhatsApp auto-login first
  const whatsAppLoginResult = await handleWhatsAppAutoLogin(request);
  if (whatsAppLoginResult) {
    return whatsAppLoginResult;
  }

  const networkConfig = (await getNetworkConfig(request)).data;
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;

  // Create headers object for setting cookies
  const headers = new Headers();
  // Set multiSeller cookie based on networkConfig
  if (networkConfig?.multiSeller !== undefined) {
    headers.set("Set-Cookie", `multiSeller=${networkConfig.multiSeller}; Path=/;`);
  }

  // Extract Click ID from URL for Facebook Conversion API tracking
  const clid = extractCLIDFromUrl(request.url);
  // Store clid in cookie and redirect to same url without clid params
  if (clid.ctwa_token) {
    const clidData = setUpCLIDData(clid, networkConfig.wabDatasetId, request.url);
    const cleanUrl = new URL(request.url);
    cleanUrl.searchParams.delete("ctwa_clid");
    cleanUrl.searchParams.delete("ctwa_token");
    headers.set("Set-Cookie", `fb_clid=${encodeURIComponent(JSON.stringify(clidData))}; Path=/; expires=${new Date(clidData.expiresAt).toUTCString()};`);
    return redirect(cleanUrl.pathname + cleanUrl.search, { headers });
  }

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, success: true, networkConfig }, { headers });
  }

  if (access_token) {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (decoded && decoded.userDetails?.minVersion) {
      return json<LoaderData>({
        minVersion: decoded.userDetails.minVersion,
        networkConfig,
        decode: decoded
      }, { headers });
    }
  }

  return json<LoaderData>({
    minVersion: 100,
    networkConfig
  }, { headers });
};

export function Layout({ children }: { children: React.ReactNode }) {
  const { pathname } = useLocation();
  const excludeSuffixes = ["/rsrp", "/cart", "/choose", "/chooseitems"];
  const showInstall = !excludeSuffixes.some((suf) => pathname.endsWith(suf));
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <Suspense fallback={<GlobalSpinnerLoader />}>
          <GlobalSpinnerLoader />
          <UserProvider>
            {children}
            <LoginBottomSheet />
            {showInstall && <InstallPWABottomSheet />}
          </UserProvider>
          <Toaster />
          <ScrollRestoration />
          <Scripts />
        </Suspense>
      </body>
    </html>
  );
}

// export default function App() {
//   return <Outlet />;
// }

// Wrap the Layout component with the HOC
// const LayoutWithPullToRefresh = withPullToRefresh(Layout);

export default function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const loader = useRouteLoaderData<LoaderData>("root");
  const networkConfig = loader!.networkConfig;

  const { setNetworkConfig } = useAppConfigStore((state) => state);
  const { setDecodedToken } = userStore((state) => state);
  const { forceUpdate, appInfo } = useForceUpdateCheck(
    loader?.minVersion || 100
  );

  const { authRequired } = useRequireAuth();

  const updateUrl = `market://details?id=${appInfo?.packageName}`;

  const handleRefresh = () => {
    // Revalidate the current route to fetch fresh data
    navigate(location.pathname + location.search, { replace: true });
  };

  useEffect(() => {
    setItem<NetworkConfig>("networkConfig", networkConfig);
  }, [networkConfig]);

  useEffect(() => {
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker
        .register("/service-worker.js")
        .then(() => console.log("SW registered"))
        .catch(console.error);
    }
  }, []);

  useEffect(() => {
    if (loader?.decode) {
      setDecodedToken(loader.decode);
    }
  }, [loader?.decode, setDecodedToken]);

  useEffect(() => {
    // Only set the network config - it will determine the appSource based on wabMobileNumber
    setNetworkConfig(networkConfig);
  }, [setNetworkConfig, networkConfig]);

  useEffect(() => {
    const link = document.querySelector(
      "link[rel~='icon']"
    ) as HTMLLinkElement | null;
    if (link) {
      link.href = networkConfig?.pwaAppIcon || "";
    } else {
      const newLink = document.createElement("link") as HTMLLinkElement;
      newLink.rel = "icon";
      newLink.href = networkConfig?.pwaAppIcon || "";
      document.head.appendChild(newLink);
    }
  }, [networkConfig, networkConfig?.pwaAppIcon]);

  // Initialize analytics
  useFacebookPixel(networkConfig?.wabDatasetId, location.key);
  useMicrosoftClarity();
  useHeapAnalytics(
    loader?.decode
      ? {
          userId: loader.decode.userDetails.userId,
          userName: loader.decode.userDetails.userName,
          mobileNumber: loader.decode.mobileNumber,
          buyerId: loader.decode.userDetails.buyerId
        }
      : undefined,
    networkConfig
  );

  // Track page view for Facebook Conversion API
  useFacebookPageTracking({
    enabled: true,
    pageId: undefined,
    phoneNumber: loader?.decode?.mobileNumber,
    externalId: loader?.decode?.userDetails?.userId?.toString(),
    debug: true,
  });

  // sync current location
  const currentLocation = useCurrentLocation();

  return forceUpdate ? (
    <ForceUpdate isVisible={forceUpdate} updateUrl={updateUrl} />
  ) : (
    <PullToRefreshWrapper onRefresh={handleRefresh}>
      {authRequired && location.pathname === "/" && (
        <div className="flex items-center justify-center h-screen">
          <NetworkAsset
            assetName={
              networkConfig?.ondcDomain === "RET10" ? "loginBanner" : "banner"
            }
          />
        </div>
      )}
      <Outlet />
      <WhatsappCTA />
    </PullToRefreshWrapper>
  );
}

// Global Error Boundary for unhandled errors
export { ErrorBoundary };

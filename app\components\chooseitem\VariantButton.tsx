import React from "react";
import Button from "../Button";
import { cn } from "~/utils/cn";

const VariantButton: React.FC<{
  qty: number;
  variantCount: number;
  isDisabled: boolean;
  onClick: () => void;
  version?: number;
}> = ({ qty, onClick, isDisabled, variantCount = 0, version = 1 }) => {
  console.log("qty", qty);
  return (
    <Button
      className={cn(
        "relative border p-2 rounded-md flex flex-row items-center justify-center",
        version === 1 ? "h-10 min-w-24" : "min-w-14",
        isDisabled ? "bg-white" : "bg-primary-50",
        isDisabled ? "border-neutral-600" : "border-primary",
        version === 2 && qty > 0 && "bg-primary min-w-24"
      )}
      onClick={onClick}
    >
      <span
        className={cn(
          "text-center font-bold text-xs ",
          isDisabled ? "text-neutral-800" : "text-primary ",
          isDisabled ? "border-neutral-600" : "border-primary",
          version === 2 && qty > 0 && "text-white"
        )}
      >
        {qty > 0 ? qty : "Add"}
      </span>
      <span className="absolute -bottom-1 text-[.55rem] text-primary bg-white rounded-md px-1">
        {variantCount} options
      </span>
    </Button>
  );
};

export const VariantButtonV2: React.FC<{
  qty: number;
  variantCount: number;
  isDisabled: boolean;
  onClick: () => void;
}> = ({ qty, onClick, isDisabled, variantCount = 0 }) => {
  console.log("qty", qty);
  return (
    <Button
      className={cn(
        "relative border p-2 rounded-md flex flex-row items-center justify-center gap-2 min-w-24 h-10 ",
        isDisabled ? "bg-white" : "bg-primary-50",
        isDisabled ? "border-neutral-600" : "border-primary"
      )}
      onClick={onClick}
    >
      <span
        className={`text-center ${
          isDisabled ? "text-neutral-800" : "text-primary "
        }  font-bold text-xs  ${
          isDisabled ? "border-neutral-600" : "border-primary"
        }`}
      >
        {qty > 0 ? qty : "Add"}
      </span>
      <span className="absolute -bottom-1 text-[.6rem] text-primary bg-white rounded-md px-1">
        {variantCount} options
      </span>
    </Button>
  );
};

export default VariantButton;

// Facebook Conversion API Event Builder
// Helper service for building Facebook Conversion API events

import {
  FacebookConversionEvent,
  FacebookUserData,
  FacebookCustomData,
  FacebookEventName,
  ViewContentEventData,
  AddToCartEventData,
  InitiateCheckoutEventData,
  PurchaseEventData,
} from "~/types/capi-fb";
import { hashPhoneNumberSync, hashUserDataSync } from "~/utils/capi-hash";

/**
 * Base event builder class
 */
export class FacebookEventBuilder {
  private event: Partial<FacebookConversionEvent>;

  constructor() {
    this.event = {
      action_source: "business_messaging",
      messaging_channel: "whatsapp",
      event_time: Math.floor(Date.now() / 1000), // Unix timestamp in seconds
      user_data: {},
      custom_data: {},
    };
  }

  /**
   * Set event name
   */
  setEventName(eventName: FacebookEventName): this {
    this.event.event_name = eventName;
    return this;
  }

  /**
   * Set event time
   */
  setEventTime(timestamp?: number): this {
    this.event.event_time = timestamp ? Math.floor(timestamp / 1000) : Math.floor(Date.now() / 1000);
    return this;
  }

  /**
   * Set event source URL
   */
  setEventSourceUrl(url: string): this {
    this.event.event_source_url = url;
    return this;
  }

  /**
   * Set event ID for deduplication
   */
  setEventId(eventId: string): this {
    this.event.event_id = eventId;
    return this;
  }

  /**
   * Set CLID (Click-to-WhatsApp Click ID)
   */
  setCLID(clid: string): this {
    if (!this.event.user_data) {
      this.event.user_data = {};
    }
    this.event.user_data.ctwa_clid = clid;
    return this;
  }

  /**
   * Set Facebook Page ID
   */
  setPageId(pageId: string): this {
    if (!this.event.user_data) {
      this.event.user_data = {};
    }
    this.event.user_data.page_id = pageId;
    return this;
  }

  /**
   * Set user phone number (will be hashed)
   */
  setPhoneNumber(phoneNumber: string): this {
    if (!this.event.user_data) {
      this.event.user_data = {};
    }
    try {
      this.event.user_data.ph = hashPhoneNumberSync(phoneNumber);
    } catch (error) {
      console.error("Error hashing phone number:", error);
    }
    return this;
  }

  /**
   * Set external user ID
   */
  setExternalId(externalId: string): this {
    if (!this.event.user_data) {
      this.event.user_data = {};
    }
    this.event.user_data.external_id = externalId;
    return this;
  }

  /**
   * Set user email (will be hashed)
   */
  setEmail(email: string): this {
    if (!this.event.user_data) {
      this.event.user_data = {};
    }
    try {
      this.event.user_data.em = hashUserDataSync(email.toLowerCase().trim());
    } catch (error) {
      console.error("Error hashing email:", error);
    }
    return this;
  }

  /**
   * Set currency
   */
  setCurrency(currency: string): this {
    if (!this.event.custom_data) {
      this.event.custom_data = {};
    }
    this.event.custom_data.currency = currency;
    return this;
  }

  /**
   * Set value
   */
  setValue(value: number): this {
    if (!this.event.custom_data) {
      this.event.custom_data = {};
    }
    this.event.custom_data.value = value;
    return this;
  }

  /**
   * Set content type
   */
  setContentType(contentType: string): this {
    if (!this.event.custom_data) {
      this.event.custom_data = {};
    }
    this.event.custom_data.content_type = contentType;
    return this;
  }

  /**
   * Set content IDs
   */
  setContentIds(contentIds: string[]): this {
    if (!this.event.custom_data) {
      this.event.custom_data = {};
    }
    this.event.custom_data.content_ids = contentIds;
    return this;
  }

  /**
   * Set number of items
   */
  setNumItems(numItems: number): this {
    if (!this.event.custom_data) {
      this.event.custom_data = {};
    }
    this.event.custom_data.num_items = numItems;
    return this;
  }

  /**
   * Set order ID
   */
  setOrderId(orderId: string): this {
    if (!this.event.custom_data) {
      this.event.custom_data = {};
    }
    this.event.custom_data.order_id = orderId;
    return this;
  }

  /**
   * Set custom data
   */
  setCustomData(customData: FacebookCustomData): this {
    this.event.custom_data = { ...this.event.custom_data, ...customData };
    return this;
  }

  /**
   * Set user data
   */
  setUserData(userData: FacebookUserData): this {
    this.event.user_data = { ...this.event.user_data, ...userData };
    return this;
  }

  /**
   * Build the final event
   */
  build(): FacebookConversionEvent {
    if (!this.event.event_name) {
      throw new Error("Event name is required");
    }

    return this.event as FacebookConversionEvent;
  }
}

/**
 * Create a ViewContent event
 */
export function createViewContentEvent(
  data: ViewContentEventData & {
    clid?: string;
    pageId?: string;
    phoneNumber?: string;
    externalId?: string;
    eventSourceUrl?: string;
    eventId?: string;
    timestamp?: number;
  }
): FacebookConversionEvent {
  const builder = new FacebookEventBuilder()
    .setEventName("ViewContent")
    .setContentType(data.contentType || "product")
    .setCurrency(data.currency || "INR");

  if (data.value !== undefined) builder.setValue(data.value);
  if (data.contentIds) builder.setContentIds(data.contentIds);
  if (data.contentName) builder.setCustomData({ content_name: data.contentName });
  if (data.contentCategory) builder.setCustomData({ content_category: data.contentCategory });
  if (data.clid) builder.setCLID(data.clid);
  if (data.pageId) builder.setPageId(data.pageId);
  if (data.phoneNumber) builder.setPhoneNumber(data.phoneNumber);
  if (data.externalId) builder.setExternalId(data.externalId);
  if (data.eventSourceUrl) builder.setEventSourceUrl(data.eventSourceUrl);
  if (data.eventId) builder.setEventId(data.eventId);
  if (data.timestamp) builder.setEventTime(data.timestamp);

  return builder.build();
}

/**
 * Create an AddToCart event
 */
export function createAddToCartEvent(
  data: AddToCartEventData & {
    clid?: string;
    pageId?: string;
    phoneNumber?: string;
    externalId?: string;
    eventSourceUrl?: string;
    eventId?: string;
    timestamp?: number;
  }
): FacebookConversionEvent {
  const builder = new FacebookEventBuilder()
    .setEventName("AddToCart")
    .setContentType(data.contentType || "product")
    .setCurrency(data.currency || "INR");

  if (data.value !== undefined) builder.setValue(data.value);
  if (data.contentIds) builder.setContentIds(data.contentIds);
  if (data.numItems !== undefined) builder.setNumItems(data.numItems);
  if (data.clid) builder.setCLID(data.clid);
  if (data.pageId) builder.setPageId(data.pageId);
  if (data.phoneNumber) builder.setPhoneNumber(data.phoneNumber);
  if (data.externalId) builder.setExternalId(data.externalId);
  if (data.eventSourceUrl) builder.setEventSourceUrl(data.eventSourceUrl);
  if (data.eventId) builder.setEventId(data.eventId);
  if (data.timestamp) builder.setEventTime(data.timestamp);

  return builder.build();
}

/**
 * Create an InitiateCheckout event
 */
export function createInitiateCheckoutEvent(
  data: InitiateCheckoutEventData & {
    clid?: string;
    pageId?: string;
    phoneNumber?: string;
    externalId?: string;
    eventSourceUrl?: string;
    eventId?: string;
    timestamp?: number;
  }
): FacebookConversionEvent {
  const builder = new FacebookEventBuilder()
    .setEventName("InitiateCheckout")
    .setCurrency(data.currency || "INR");

  if (data.value !== undefined) builder.setValue(data.value);
  if (data.contentIds) builder.setContentIds(data.contentIds);
  if (data.numItems !== undefined) builder.setNumItems(data.numItems);
  if (data.clid) builder.setCLID(data.clid);
  if (data.pageId) builder.setPageId(data.pageId);
  if (data.phoneNumber) builder.setPhoneNumber(data.phoneNumber);
  if (data.externalId) builder.setExternalId(data.externalId);
  if (data.eventSourceUrl) builder.setEventSourceUrl(data.eventSourceUrl);
  if (data.eventId) builder.setEventId(data.eventId);
  if (data.timestamp) builder.setEventTime(data.timestamp);

  return builder.build();
}

/**
 * Create a Purchase event
 */
export function createPurchaseEvent(
  data: PurchaseEventData & {
    clid?: string;
    pageId?: string;
    phoneNumber?: string;
    externalId?: string;
    eventSourceUrl?: string;
    eventId?: string;
    timestamp?: number;
  }
): FacebookConversionEvent {
  const builder = new FacebookEventBuilder()
    .setEventName("Purchase")
    .setCurrency(data.currency)
    .setValue(data.value);

  if (data.orderId) builder.setOrderId(data.orderId);
  if (data.contentIds) builder.setContentIds(data.contentIds);
  if (data.numItems !== undefined) builder.setNumItems(data.numItems);
  if (data.clid) builder.setCLID(data.clid);
  if (data.pageId) builder.setPageId(data.pageId);
  if (data.phoneNumber) builder.setPhoneNumber(data.phoneNumber);
  if (data.externalId) builder.setExternalId(data.externalId);
  if (data.eventSourceUrl) builder.setEventSourceUrl(data.eventSourceUrl);
  if (data.eventId) builder.setEventId(data.eventId);
  if (data.timestamp) builder.setEventTime(data.timestamp);

  return builder.build();
}

/**
 * Generate a unique event ID for deduplication
 */
export function generateEventId(prefix: string = "EVT"): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `${prefix}-${timestamp}-${random}`;
}

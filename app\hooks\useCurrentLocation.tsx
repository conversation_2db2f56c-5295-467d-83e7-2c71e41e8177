import { useEffect, useState } from "react";

export const useCurrentLocation = () => {
  const [latitude, setLatitude] = useState<number | null>(null);
  const [longitude, setLongitude] = useState<number | null>(null);
  const [error, setError] = useState<GeolocationPositionError | null>(null);

  useEffect(() => {
    refresh();
  }, []);

  const request = (
    onSuccess?: (position: GeolocationPosition) => void,
    onError?: (error: GeolocationPositionError) => void
  ) => {
    // Request current location
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const coords = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
        };

        setLatitude(coords.latitude);
        setLongitude(coords.longitude);
        setError(null);
        // Set in cookie (stringify + encode)
        document.cookie = `coordinates=${encodeURIComponent(JSON.stringify(coords))}; path=/; max-age=604800`;

        onSuccess && onSuccess(position);
      },
      (error) => {
        console.error("Error requesting location:", error);
        setLatitude(null);
        setLongitude(null);
        setError(error);
        // Clear cookie
        document.cookie = `coordinates=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC; max-age=0`;

        onError && onError(error);
      },
      { enableHighAccuracy: true, maximumAge: 0 }
    );
  };

  const refresh = () => {
    // Check if geolocation permission is granted
    navigator.permissions.query({ name: 'geolocation' }).then((result) => {
      if (result.state === 'granted') {
        // Request current location
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const coords = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
            };

            setLatitude(coords.latitude);
            setLongitude(coords.longitude);
            setError(null);
            // Set in cookie (stringify + encode)
            document.cookie = `coordinates=${encodeURIComponent(JSON.stringify(coords))}; path=/; max-age=604800`;
          },
          (error) => {
            console.error("Error refreshing location:", error.message);
            setLatitude(null);
            setLongitude(null);
            setError(error);
            // Clear cookie
            document.cookie = `coordinates=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC; max-age=0`;
          },
          { enableHighAccuracy: true, maximumAge: 0 }
        );
      } else {
        setLatitude(null);
        setLongitude(null);
        setError(null);
        document.cookie = `coordinates=; path=/; expires=Thu, 01 Jan 1970 00:00:00 UTC; max-age=0`;
      }
    });
  };

  return { latitude, longitude, error, request, refresh };
};

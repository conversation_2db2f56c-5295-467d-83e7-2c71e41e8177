import { But<PERSON> } from "@headlessui/react";
// import dayjs from "dayjs";
import React from "react";
import TruncatedText from "../TruncatedText";
import { useNavigate } from "@remix-run/react";
import { AddressDto } from "~/types/address.types";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import {
  ChevronDown,
  CircleUserRound,
  MapPin,
  User,
  UserCircle,
  UserRound
} from "lucide-react";
import { useAppConfigStore } from "~/stores/appConfig.store";

interface NotDeliveringMessageProps {
  onAddressClick: () => void;
  visible?: boolean;
}

const NotDeliveringMessage: React.FC<NotDeliveringMessageProps> = ({
  onAddressClick,
  visible = true
}) => {
  if (!visible) return null;

  return (
    <Button className="w-full" onClick={onAddressClick}>
      <div className="flex flex-col w-full items-start gap-2 rounded-md bg-orange-100 p-2 text-black mt-2">
        <div className="text-xs font-bold p-2 bg-orange-800 rounded-md text-white">
          NOT DELIVERING
        </div>
        <p className="text-xs font-light">
          {"We do not deliver in your area since it's far away."}
        </p>
      </div>
    </Button>
  );
};

interface SellerProps {
  defaultAddress: AddressDto;
  onAddressClick?: () => void;
  onProfileClick?: () => void;
}

const RestaurantDeliveryInfo: React.FC<SellerProps> = ({
  defaultAddress,
  onAddressClick,
  onProfileClick
}) => {
  const navigate = useNavigate();
  const { itemOptionsData } = chooseitemsStore((state) => state);
  const { appDomain } = useAppConfigStore((state) => state);

  const handleAddressClick = () => {
    if (onAddressClick) {
      onAddressClick();
    } else {
      if (appDomain === "RET11") {
        navigate(`/changeaddress?redirectTo=/home/<USER>/rsrp`, {
          state: {
            address: defaultAddress,
            isEdit: true
          }
        });
      } else {
        navigate(`/changeaddress?redirectTo=/chooseitems`, {
          state: {
            address: defaultAddress,
            isEdit: true
          }
        });
      }
    }
  };
  return (
    <>
      {
        <div className="flex flex-col justify-between items-center px-3 pt-4">
          <div className="flex justify-between items-center w-full">
            <div className="flex-grow">
              <Button
                className="text-sm flex items-center gap-2"
                onClick={handleAddressClick}
              >
                <div className="">
                  <MapPin size={32} />
                </div>
                <div className="flex flex-col items-start">
                  <div className="flex items-center gap-1 text-sm font-semibold">
                    {(defaultAddress?.address &&
                      defaultAddress.name?.toLocaleUpperCase()) ||
                      "Add your delivery address"}
                    <ChevronDown className="w-4 h-4" />
                  </div>
                  <TruncatedText
                    className="text-[.7rem] font-light max-w-[16rem]"
                    text={
                      defaultAddress.address ||
                      "Check availability and menu items at your location"
                    }
                  />
                </div>
              </Button>
            </div>
            <div>
              <Button
                onClick={onProfileClick}
                className="rounded-full bg-black bg-opacity-30 p-2"
              >
                <UserRound size={20} fill="#FFF" strokeWidth={0} />
              </Button>
            </div>
          </div>
          {/* {!itemOptionsData?.defaultAddress.buyerInServiceArea &&
            parseInt(defaultAddress.latitude) !== 0 &&
            parseInt(defaultAddress.longitude) !== 0 && (
              <NotDeliveringMessage
                onAddressClick={handleAddressClick}
                visible={!itemOptionsData?.defaultAddress.buyerInServiceArea}
              />
            )} */}
        </div>
      }
    </>
  );
};

export default RestaurantDeliveryInfo;

// Conversion API Logging Utility
// Provides structured logging for Conversion API events and errors

/**
 * Log levels for Conversion API
 */
export enum LogLevel {
  DEBUG = "debug",
  INFO = "info",
  WARN = "warn",
  ERROR = "error",
}

/**
 * Log entry structure
 */
interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: any;
  error?: Error;
  eventId?: string;
  clid?: string;
  userId?: string;
}

/**
 * Conversion API Logger
 */
export class Logger {
  private static instance: Logger;
  private isDebugEnabled: boolean;
  private logBuffer: LogEntry[] = [];
  private maxBufferSize = 100;

  private constructor() {
    // this.isDebugEnabled = process.env.NODE_ENV === "development";
    this.isDebugEnabled = true;
  }

  /**
   * Get singleton instance
   */
  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Enable or disable debug logging
   */
  setDebugEnabled(enabled: boolean): void {
    this.isDebugEnabled = enabled;
  }

  /**
   * Log a debug message
   */
  debug(message: string, context?: any): void {
    if (this.isDebugEnabled) {
      this.log(LogLevel.DEBUG, message, context);
    }
  }

  /**
   * Log an info message
   */
  info(message: string, context?: any): void {
    this.log(LogLevel.INFO, message, context);
  }

  /**
   * Log a warning message
   */
  warn(message: string, context?: any): void {
    this.log(LogLevel.WARN, message, context);
  }

  /**
   * Log an error message
   */
  error(message: string, error?: Error, context?: any): void {
    this.log(LogLevel.ERROR, message, context, error);
  }

  /**
   * Log CLID-related events
   */
  logCLID(action: "extracted" | "stored" | "retrieved" | "expired", clid?: string, context?: any): void {
    const message = `CLID ${action}${clid ? ` (${clid.substring(0, 10)}...)` : ""}`;
    this.debug(message, { action, clid: clid ? clid.substring(0, 10) + "..." : null, ...context });
  }

  /**
   * Log event tracking
   */
  logEvent(
    action: "queued" | "sent" | "failed" | "retried",
    eventName: string,
    eventId?: string,
    context?: any
  ): void {
    const message = `Event ${action}: ${eventName}${eventId ? ` (${eventId})` : ""}`;
    const level = action === "failed" ? LogLevel.ERROR : LogLevel.DEBUG;
    this.log(level, message, { action, eventName, eventId, ...context });
  }

  /**
   * Log API requests
   */
  logApiRequest(
    method: "GET" | "POST",
    url: string,
    status?: number,
    duration?: number,
    context?: any
  ): void {
    const message = `API ${method} ${url}${status ? ` - ${status}` : ""}${duration ? ` (${duration}ms)` : ""}`;
    const level = status && status >= 400 ? LogLevel.ERROR : LogLevel.DEBUG;
    this.log(level, message, { method, url, status, duration, ...context });
  }

  /**
   * Log configuration issues
   */
  logConfig(issue: string, context?: any): void {
    this.warn(`Configuration: ${issue}`, context);
  }

  /**
   * Log user data processing
   */
  logUserData(action: "hashed" | "validated" | "error", field: string, context?: any): void {
    const message = `User data ${action}: ${field}`;
    const level = action === "error" ? LogLevel.ERROR : LogLevel.DEBUG;
    this.log(level, message, { action, field, ...context });
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, message: string, context?: any, error?: Error): void {
    const logEntry: LogEntry = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
      error,
    };

    // Add to buffer
    this.addToBuffer(logEntry);

    // Console output
    this.outputToConsole(logEntry);

    // Send to external logging service if configured
    // this.sendToExternalLogger(logEntry);
  }

  /**
   * Add log entry to buffer
   */
  private addToBuffer(entry: LogEntry): void {
    this.logBuffer.push(entry);
    
    // Keep buffer size manageable
    if (this.logBuffer.length > this.maxBufferSize) {
      this.logBuffer = this.logBuffer.slice(-this.maxBufferSize);
    }
  }

  /**
   * Output to console
   */
  private outputToConsole(entry: LogEntry): void {
    const prefix = `[Conversion API] [${entry.level.toUpperCase()}]`;
    const message = `${prefix} ${entry.message}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        if (this.isDebugEnabled) {
          console.debug(message, entry.context);
        }
        break;
      case LogLevel.INFO:
        console.info(message, entry.context);
        break;
      case LogLevel.WARN:
        console.warn(message, entry.context);
        break;
      case LogLevel.ERROR:
        console.error(message, entry.error || entry.context);
        break;
    }
  }

  /**
   * Send to external logging service
   */
  private sendToExternalLogger(entry: LogEntry): void {
    // Implement external logging service integration here
    // For example, send to Sentry, LogRocket, or custom logging endpoint
    
    if (entry.level === LogLevel.ERROR && typeof window !== "undefined") {
      // Example: Send errors to a logging endpoint
      try {
        fetch("/api/logs/capi", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(entry),
        }).catch(() => {
          // Silently fail to avoid infinite loops
        });
      } catch {
        // Silently fail
      }
    }
  }

  /**
   * Get recent logs
   */
  getRecentLogs(count: number = 50): LogEntry[] {
    return this.logBuffer.slice(-count);
  }

  /**
   * Clear log buffer
   */
  clearLogs(): void {
    this.logBuffer = [];
  }

  /**
   * Get logs as formatted string
   */
  getLogsAsString(): string {
    return this.logBuffer
      .map(entry => `[${entry.timestamp}] [${entry.level.toUpperCase()}] ${entry.message}`)
      .join("\n");
  }

  /**
   * Export logs for debugging
   */
  exportLogs(): string {
    const logs = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      debugEnabled: this.isDebugEnabled,
      entries: this.logBuffer,
    };
    
    return JSON.stringify(logs, null, 2);
  }
}

/**
 * Convenience functions for logging
 */
export const logger = Logger.getInstance();

export const logDebug = (message: string, context?: any) => 
  logger.debug(message, context);

export const logInfo = (message: string, context?: any) => 
  logger.info(message, context);

export const logWarn = (message: string, context?: any) => 
  logger.warn(message, context);

export const logError = (message: string, error?: Error, context?: any) => 
  logger.error(message, error, context);

export const logCLID = (action: "extracted" | "stored" | "retrieved" | "expired", clid?: string, context?: any) =>
  logger.logCLID(action, clid, context);

export const logEvent = (action: "queued" | "sent" | "failed" | "retried", eventName: string, eventId?: string, context?: any) =>
  logger.logEvent(action, eventName, eventId, context);

export const logApiRequest = (method: "GET" | "POST", url: string, status?: number, duration?: number, context?: any) =>
  logger.logApiRequest(method, url, status, duration, context);

/**
 * Error wrapper for Conversion API operations
 */
export function withErrorHandling<T extends any[], R>(
  operation: (...args: T) => R,
  operationName: string
): (...args: T) => R {
  return (...args: T): R => {
    try {
      const result = operation(...args);
      
      // Handle promises
      if (result instanceof Promise) {
        return result.catch((error: Error) => {
          logError(`${operationName} failed`, undefined, { args });
          throw error;
        }) as R;
      }
      
      return result;
    } catch (error) {
      logError(`${operationName} failed`, undefined, { args });
      throw error;
    }
  };
}

/**
 * Performance monitoring wrapper
 */
export function withPerformanceMonitoring<T extends any[], R>(
  operation: (...args: T) => R,
  operationName: string
): (...args: T) => R {
  return (...args: T): R => {
    const startTime = Date.now();
    
    try {
      const result = operation(...args);
      
      // Handle promises
      if (result instanceof Promise) {
        return result.finally(() => {
          const duration = Date.now() - startTime;
          logDebug(`${operationName} completed`, { duration, args });
        }) as R;
      }
      
      const duration = Date.now() - startTime;
      logDebug(`${operationName} completed`, { duration, args });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      logError(`${operationName} failed after ${duration}ms`, undefined, { args });
      throw error;
    }
  };
}

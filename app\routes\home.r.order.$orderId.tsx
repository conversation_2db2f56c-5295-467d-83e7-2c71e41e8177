import { json, LoaderFunction, LoaderFunctionArgs } from "@remix-run/node";
import { useNavigate, useLoaderData, useFetcher } from "@remix-run/react";
import { Order } from "~/types";
import OrderTimeline from "~/components/orders/OrderTimeline";
import OrderItemList from "~/components/orders/OrderItemList";
import OrderBillSummary from "~/components/orders/OrderBillSummary";
import OrderAdditionalDetails from "~/components/orders/OrderAdditionalDetails";
import { ArrowLeftIcon } from "lucide-react";
import PayNow from "~/components/PayNow";
import { formatCurrency } from "~/utils/format";
import { orderToPaymentRequest } from "~/types/payment";
import { useEffect, useState, useRef } from "react";
import { useHomeStore } from "~/stores/home.store";
import { requireAuth } from "~/utils/clientReponse";
import { useRequireAuth } from "~/hooks/useRequireAuth";
import Button from "~/components/Button";
import OrderStatusCard from "~/components/orders/OrderStatusCard";
import { getOrderDetailsAPI } from "~/services/buyer.service";
import OrderDeliveryOTP from "~/components/orders/OrderDeliveryOTP";
import { ORDER_POLLING_INTERVAL, ORDER_POLLING_MAX_COUNT } from "~/utils/constant";

export interface LoaderData {
  order: Order | null;
  error?: string;
}

// Loader function to handle unauthorized access
export const loader: LoaderFunction = async ({
  params,
  request
}: LoaderFunctionArgs) => {
  const orderId = params.orderId;
  if (!orderId) {
    return json(
      { order: null, error: "Order ID is required" },
      { status: 400 }
    );
  }
  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json({ ...auth, order: null });
  }

  try {
    const response = await getOrderDetailsAPI(orderId, request);
    return json({ order: response.data, error: undefined });
  } catch (error) {
    console.error("Error fetching order details:", error);
    return json(
      { order: null, error: "Failed to fetch order details" },
      { status: 500 }
    );
  }
};

export default function OrderDetails() {
  const navigate = useNavigate();
  const loaderData = useLoaderData<LoaderData>();
  const [order, setOrder] = useState<Order | null>(loaderData.order as Order | null);
  const orderFetcher = useFetcher<LoaderData>();
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const pollCountRef = useRef(0);
  
  useRequireAuth();

  // hide bottom bar
  useEffect(() => {
    useHomeStore.setState({ hideBottomBar: true });

    return () => useHomeStore.setState({ hideBottomBar: false });
  }, []);

  // Setup polling for order updates
  useEffect(() => {
    // Only poll if order is in an active status
    const shouldPoll = order && order.status !== "Delivered" && order.status !== "Cancelled";
    
    // Clear any existing interval
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }
    
    // Set up polling if needed
    if (shouldPoll) {
    pollingIntervalRef.current = setInterval(() => {
      if (pollCountRef.current >= ORDER_POLLING_MAX_COUNT) {
        clearInterval(pollingIntervalRef.current!);
        return;
      }
      pollCountRef.current += 1;
      orderFetcher.load(`/home/<USER>/order/${order.id}`);
    }, ORDER_POLLING_INTERVAL);
  }
    
    // Cleanup on unmount
    return () => {
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
      }
    };
  }, [order?.id, order?.status]);
  
  // Update order state when fetcher returns new data
  useEffect(() => {
    if (orderFetcher.data?.order) {
      setOrder(orderFetcher.data.order as Order);
    }
  }, [orderFetcher.data]);

  const handleBack = () => {
    navigate(-1);
  };

  if (!order) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-gray-500">No order details available</p>
      </div>
    );
  }

  const showPayNow = order.balanceTobePaid > 0;
  const showDeliveryOtp = order.deliveryCode && order.deliveryCode !== "0000" && order.status !== "Delivered" && order.status !== "Cancelled";

  return (
    <div className="flex flex-col bg-gray-100 min-h-screen">
      {/* Header with back button and title */}
      <div className="fixed top-0 z-10 bg-white px-4 py-3 flex items-center  justify-between border-b w-full">
        <div className="flex flex-row items-center justify-center">
          <button className="mr-4" onClick={handleBack}>
            <ArrowLeftIcon className="w-5 h-5 text-gray-500" />
          </button>
          <h1 className="text-lg font-semibold">Order Details</h1>
        </div>
        <div>
          <Button
            onClick={() => navigate(`/help?orderId=${order.id}&action=create`)}
            className="text-md text-primary font-medium"
          >
            Support
          </Button>
        </div>
      </div>

      <div className="flex-grow p-4 space-y-4 mb-20 mt-12">
        {order.fulfillmentType === "DELIVERY" ? (
          <OrderStatusCard order={order} />
        ) : (
          <OrderTimeline
            orderDetails={order}
            orderItems={order.farmers
              .flatMap((farmer) => farmer.items || [])
              .map((item) => ({
                itemName: item.itemName,
                itemUrl: item.itemUrl,
                qty: item.qty,
                price: item.price,
                amount: item.amount,
                status: item.status,
                unit: item.unit,
                cancelledQty: item.cancelledQty,
                returnedQty: item.returnedQty,
                diet: item.diet,
                strikeOffAmount: item.strikeOffAmount,
                itemTaxAmount: item.itemTaxAmount
              }))}
          />
        )}
        {showDeliveryOtp && <OrderDeliveryOTP order={order} />}
        <OrderItemList order={order} />
        <OrderBillSummary order={order} />
        <OrderAdditionalDetails order={order} />
      </div>

      {showPayNow && (
        <div className="fixed bottom-2 z-10 px-2 w-full">
          <PayNow
            paymentRequest={orderToPaymentRequest(order)}
            buttonClassName="z-10 bottom-0 p-3 rounded-xl flex justify-center align-center gap-1 w-full bg-primary hover:bg-primary-600 text-white shadow-sm"
            buttonText={`Pay ${formatCurrency(order.balanceTobePaid)}`}
            onSuccess={() => navigate(0)}
          />
        </div>
      )}
    </div>
  );
}

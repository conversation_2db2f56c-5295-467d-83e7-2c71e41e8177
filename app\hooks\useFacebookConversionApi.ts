// Facebook Conversion API React Hook
// Client-side hook for tracking events and sending them to the server

import { useCallback, useEffect, useRef } from "react";
import { useLocation } from "@remix-run/react";
import {
  FacebookEventName,
  ViewContentEventData,
  AddToCartEventData,
  InitiateCheckoutEventData,
  PurchaseEventData,
} from "~/types/capi-fb";
import { logEvent, logDebug, logError } from "~/utils/capi-logger";

/**
 * Event data for tracking
 */
interface EventTrackingData {
  eventName: FacebookEventName;
  eventData?: any;
  eventId?: string;
  timestamp?: number;
}

/**
 * Configuration for the Facebook Conversion API hook
 */
interface UseFacebookConversionApiConfig {
  /** Whether Facebook Conversion API is enabled */
  enabled?: boolean;
  /** Facebook Page ID */
  pageId?: string;
  /** User's phone number */
  phoneNumber?: string;
  /** External user ID */
  externalId?: string;
  /** Whether to enable debug logging */
  debug?: boolean;
  /** Batch size for sending events */
  batchSize?: number;
  /** Batch timeout in milliseconds */
  batchTimeout?: number;
}

/**
 * Hook for Facebook Conversion API tracking
 */
export function useFacebookConversionApi(config: UseFacebookConversionApiConfig = {}) {
  const location = useLocation();
  const eventQueueRef = useRef<EventTrackingData[]>([]);
  const batchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const {
    enabled = false,
    pageId,
    phoneNumber,
    externalId,
    debug = false,
    batchSize = 1,
    batchTimeout = 5000,
  } = config;

  /**
   * Send events to the server
   */
  const sendEventsToServer = useCallback(async (events: EventTrackingData[]) => {
    if (!enabled || events.length === 0) {
      logDebug("No events to send", { enabled, eventCount: events.length });
      return;
    }

    const eventNames = events.map(e => e.eventName).join(", ");
    logDebug("Sending events to server", {
      eventCount: events.length,
      eventNames,
    });

    try {
      const response = await fetch("/api/capi-fb", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ events }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
      }

      const result = await response.json();

      logEvent("sent", `${events.length} events`, undefined, {
        eventNames,
        eventsProcessed: result.eventsProcessed,
        success: result.success,
      });

    } catch (error) {
      logError("Failed to send events to server", error as Error, {
        eventCount: events.length,
        eventNames,
      });

      // Mark events as failed for potential retry
      events.forEach(event => {
        logEvent("failed", event.eventName, event.eventId, {
          error: error.message,
        });
      });
    }
  }, [enabled, debug]);

  /**
   * Flush the event queue
   */
  const flushEventQueue = useCallback(async () => {
    if (eventQueueRef.current.length === 0) {
      return;
    }

    const eventsToSend = [...eventQueueRef.current];
    eventQueueRef.current = [];

    // Clear any pending batch timeout
    if (batchTimeoutRef.current) {
      clearTimeout(batchTimeoutRef.current);
      batchTimeoutRef.current = null;
    }

    await sendEventsToServer(eventsToSend);
  }, [sendEventsToServer]);

  /**
   * Add event to queue and potentially flush
   */
  const queueEvent = useCallback((eventData: EventTrackingData) => {
    if (!enabled) {
      logDebug("Event not queued - tracking disabled", { eventName: eventData.eventName });
      return;
    }

    const enrichedEventData = {
      ...eventData,
      timestamp: eventData.timestamp || Date.now(),
    };

    eventQueueRef.current.push(enrichedEventData);

    logEvent("queued", eventData.eventName, eventData.eventId, {
      queueLength: eventQueueRef.current.length,
      batchSize,
    });

    // Check if we should flush immediately
    if (eventQueueRef.current.length >= batchSize) {
      logDebug("Batch size reached, flushing immediately", {
        queueLength: eventQueueRef.current.length,
        batchSize,
      });
      flushEventQueue();
    } else {
      // Set up batch timeout if not already set
      if (!batchTimeoutRef.current) {
        logDebug("Setting batch timeout", { batchTimeout });
        batchTimeoutRef.current = setTimeout(() => {
          flushEventQueue();
        }, batchTimeout);
      }
    }
  }, [enabled, batchSize, batchTimeout, flushEventQueue, debug]);

  /**
   * Track ViewContent event
   */
  const trackViewContent = useCallback(({ eventName, eventId, timestamp, eventData } : { eventName: FacebookEventName, eventId?: string, timestamp?: number, eventData?: ViewContentEventData }) => {
    queueEvent({
      eventName: "ViewContent",
      eventData: {...eventData, phoneNumber, externalId },
      eventId: eventId,
      timestamp: timestamp,
    });
  }, [queueEvent]);

  /**
   * Track AddToCart event
   */
  const trackAddToCart = useCallback(({ eventName, eventId, timestamp, eventData } : { eventName: FacebookEventName, eventId?: string, timestamp?: number, eventData?: AddToCartEventData }) => {
    queueEvent({
      eventName: "AddToCart",
      eventData: {...eventData, phoneNumber, externalId },
      eventId: eventId,
      timestamp: timestamp,
    });
  }, [queueEvent]);

  /**
   * Track InitiateCheckout event
   */
  const trackInitiateCheckout = useCallback(({ eventName, eventId, timestamp, eventData } : { eventName: FacebookEventName, eventId?: string, timestamp?: number, eventData?: InitiateCheckoutEventData }) => {
    queueEvent({
      eventName: "InitiateCheckout",
      eventData: {...eventData, phoneNumber, externalId },
      eventId: eventId,
      timestamp: timestamp,
    });
  }, [queueEvent]);

  /**
   * Track Purchase event
   */
  const trackPurchase = useCallback(({ eventName, eventId, timestamp, eventData } : { eventName: FacebookEventName, eventId?: string, timestamp?: number, eventData?: PurchaseEventData }) => {
    queueEvent({
      eventName: "Purchase",
      eventData: {...eventData, phoneNumber, externalId },
      eventId: eventId,
      timestamp: timestamp,
    });
  }, [queueEvent]);

  /**
   * Track custom event
   */
  const trackCustomEvent = useCallback((
    { eventName, eventId, timestamp, eventData } : { eventName: FacebookEventName, eventId?: string, timestamp?: number, eventData?: any }
  ) => {
    queueEvent({
      eventName,
      eventData: {...eventData, phoneNumber, externalId },
      eventId,
      timestamp,
    });
  }, [queueEvent]);

  /**
   * Flush events on page unload
   */
  useEffect(() => {
    if (!enabled) {
      return;
    }

    const handleBeforeUnload = () => {
      // Use sendBeacon for reliable event sending on page unload
      if (eventQueueRef.current.length > 0 && navigator.sendBeacon) {
        const payload = JSON.stringify({ events: eventQueueRef.current });
        
        navigator.sendBeacon("/api/capi-fb", payload);
        eventQueueRef.current = [];
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      
      // Flush any remaining events
      if (eventQueueRef.current.length > 0) {
        flushEventQueue();
      }
      
      // Clear timeout
      if (batchTimeoutRef.current) {
        clearTimeout(batchTimeoutRef.current);
      }
    };
  }, [enabled, flushEventQueue]);

  return {
    // Event tracking methods
    trackViewContent,
    trackAddToCart,
    trackInitiateCheckout,
    trackPurchase,
    trackCustomEvent,
    
    // Utility methods
    flushEventQueue,
    
    // Status
    isEnabled: enabled,
  };
}

/**
 * Hook for automatic page view tracking
 */
export function useFacebookPageTracking(config: UseFacebookConversionApiConfig = {}) {
  const location = useLocation();
  const { trackViewContent, isEnabled } = useFacebookConversionApi(config);

  useEffect(() => {
    if (!isEnabled) {
      return;
    }

    const currentPath = location.pathname + location.search;

    // Track page view as ViewContent event
    trackViewContent({
      eventName: "ViewContent",
      eventId: `EVT-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      timestamp: Date.now(),
      eventData: {
        contentType: "page",
        contentName: currentPath,
      },
    }
    );
  }, [location.key, trackViewContent, isEnabled]);

  return {
    isEnabled,
  };
}

import { getDomainFromRequest } from "@utils/domain";
import { apiRequest, getApiUrl } from "@utils/api";
import { ApiResponse, MnetCoreResponse } from "~/types/Api";
import { AddressDto, BuyerAddressDto } from "~/types/address.types";

export async function getAddressListAPI(
  request: Request
): Promise<ApiResponse<AddressDto[]>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const url = getApiUrl(
    `/addressv2`,
    undefined,
    undefined,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<AddressDto[]>(
      url,
      "GET",
      null,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Get address api failed");
    }
  } catch (error) {
    console.error("Error changing address:", error);
    throw error;
  }
}

export async function addAddressAPI(
  payload: BuyerAddressDto,
  request: Request
): Promise<ApiResponse<MnetCoreResponse<AddressDto>>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const url = getApiUrl(
    `/addressv2`,
    undefined,
    undefined,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<MnetCoreResponse<AddressDto>>(
      url,
      "POST",
      payload,
      {},
      true,
      request
    );
    return response;
  } catch (error) {
    console.error("Error adding address:", error);
    throw error;
  }
}

export async function updateAddressAPI(
  payload: {
    addressId: number;
    address: BuyerAddressDto;
    cName?: string;
  },
  request: Request
): Promise<ApiResponse<MnetCoreResponse<AddressDto>>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);

  const url = getApiUrl(
    `/address/${payload.addressId}` +
      (payload.cName ? `?cName=${encodeURIComponent(payload.cName)}` : ""),
    undefined,
    undefined,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<MnetCoreResponse<AddressDto>>(
      url,
      "PUT",
      payload.address,
      {},
      true,
      request
    );
    return response;
  } catch (error) {
    console.error("Error updating address:", error);
    throw error;
  }
}

export async function markDefaultAddressAPI(
  payload: {
    addressId: number;
  },
  request: Request
): Promise<ApiResponse<MnetCoreResponse<AddressDto[]>>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const url = getApiUrl(
    `/address/${payload.addressId}/default`,
    undefined,
    undefined,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<MnetCoreResponse<AddressDto[]>>(
      url,
      "PUT",
      null,
      {},
      true,
      request
    );
    return response;
  } catch (error) {
    console.error("Error updating address:", error);
    throw error;
  }
}

export async function deleteAddressAPI(
  payload: {
    addressId: number;
    address: BuyerAddressDto;
  },
  request: Request
): Promise<ApiResponse<MnetCoreResponse<AddressDto>>> {
  return await updateAddressAPI(
    {
      addressId: payload.addressId,
      address: { ...payload.address, disable: true }
    },
    request
  );
}

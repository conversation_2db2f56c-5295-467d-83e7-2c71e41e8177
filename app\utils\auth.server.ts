import { redirect } from "@remix-run/node";
import { verifyOtp } from "~/services/auth.server";
import { getSession, commitSession } from "~/utils/session.server";
import { parseJWT } from "./token-utils";
import { User } from "~/types";

export async function handleWhatsAppAutoLogin(request: Request) {
  const session = await getSession(request.headers.get("Cookie"));
  const url = new URL(request.url);
  const token = url.searchParams.get("token");
  const mobileNumber = url.searchParams.get("mobileNumber");

  // If user is already logged in, no need to proceed
  // if (session.has("access_token")) {
  //   return null;
  // }

  // If we don't have token or mobileNumber, can't proceed with auto-login
  if (!token || !mobileNumber) {
    return null;
  }

  try {
    const verifyResponse = (
      await verifyOtp(mobileNumber, token, "whatsAppLogin", request)
    ).data;

    session.set("access_token", verifyResponse.access_token ?? "");
    session.set("refresh_token", verifyResponse.refresh_token ?? "");

    // Handle registration redirect if needed
    if (verifyResponse.roles?.includes("USER_REGISTRATION")) {
      return redirect(
        `/registration?phoneNumber=${mobileNumber}&token=${verifyResponse.access_token}`,
        {
          headers: {
            "Set-Cookie": await commitSession(session)
          }
        }
      );
    }

    const tokenData = parseJWT(verifyResponse.access_token ?? "");
    const userDetails = tokenData.userDetails;

    const user: User = {
      userId: userDetails.userId,
      userName: userDetails.userName,
      businessName: userDetails.businessName,
      buyerId: userDetails.buyerId
    };
    session.set("user", user);

    // Redirect to the same page but without the token and mobileNumber params
    const cleanUrl = new URL(request.url);
    cleanUrl.searchParams.delete("token");
    cleanUrl.searchParams.delete("mobileNumber");

    return redirect(cleanUrl.pathname + cleanUrl.search, {
      headers: {
        "Set-Cookie": await commitSession(session)
      }
    });
  } catch (error) {
    // If auto-login fails, just return null and let the normal flow handle it
    console.error("WhatsApp auto-login failed:", error);
    return null;
  }
}

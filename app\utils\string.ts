export function isValidNumber(value: string | number): boolean {
  if (value === "") {
    return true;
  }

  if (
    typeof value === "number" ||
    (typeof value === "string" && /^[+-]?\d+(\.\d+)?$/.test(value))
  ) {
    return true;
  }

  return false;
}

export const isValidPhoneNo = (phoneNumber: string) => {
  return /^\d{10}$/.test(phoneNumber);
};

export function capitalizeFirstLetter(string: string) {
  if (!string) return ""; // Handle empty or null input
  return string.charAt(0).toUpperCase() + string.slice(1);
}

export const formatPrice = (price: number) =>
  price % 1 === 0 ? price.toFixed(0) : price.toFixed(2);

export function isEmptyNullOrUndefinedString(input: string) {
  return (
    input === null ||
    input === undefined ||
    /^(null|undefined|\s*)$/.test(input)
  );
}

export function getImagesUrlsFromString(imageUrl: string) {
  if (!imageUrl) {
    return [];
  }
  return imageUrl.split(",");
}

export function getFirstImageFromString(imageUrl: string) {
  if (!imageUrl) {
    return "";
  }
  return imageUrl.split(",")?.[0];
}

export function camelCaseToWords(text: string) {
  return text
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase());
}

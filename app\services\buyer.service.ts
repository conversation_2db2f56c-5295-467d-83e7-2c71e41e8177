// app/services/buyer.service.ts

import { apiRequest, getApiUrl } from "@utils/api";
import {
  SellerOption,
  ItemOptionsData,
  PrecheckOrderResponse,
  PrecheckOrderPayload,
  ConfirmOrderPayload,
  OrderResponse,
  InitiatePayment,
  ConfirmOrderResponse,
  UpiPaymentStatusDetails,
  MoneyCollectionDetails,
  Order
} from "~/types";
import { getDomainFromRequest } from "@utils/domain";
import {
  Address,
  SupportTicket,
  Transaction,
  WalletDetails
} from "~/types/user";
import { ApiResponse, MnetCoreResponse } from "~/types/Api";

/**
 * Fetches seller options based on buyer ID and delivery date.
 * @param buyerId - The ID of the buyer.
 * @param deliveryDate - The delivery date in YYYY-MM-DD format.
 * @param access_token - The authentication token.
 * @returns A promise that resolves to an array of SellerOption.
 */

// const API_BASE_URL = process.env.API_BASE_URL;
const MOCK_API_BASE_URL =
  "https://6bfe032c-8f37-477f-bb19-bf17621c9d3e.mock.pstmn.io";
const fNodeEnv = process.env.F_NODE_ENV;

export async function getSellerOptionsAPI(
  deliveryDate: string,
  request: Request
): Promise<ApiResponse<SellerOption[]>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const params = { deliveryDate };
  const url = getApiUrl(
    "/selleroptions",
    undefined,
    params,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<SellerOption[]>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("No seller options found");
    }
  } catch (error) {
    console.error("Error fetching seller options:", error);
    throw error;
  }
}

/**
 * Fetches item options based on buyer ID, delivery date, and seller ID.
 * @param buyerId - The ID of the buyer.
 * @param deliveryDate - The delivery date in YYYY-MM-DD format.
 * @param sellerId - The ID of the seller.
 * @param access_token - The authentication token.
 * @returns A promise that resolves to ItemOptionsData.
 */
export async function getItemOptionsAPI(
  mobileNumber: string | null,
  request: Request,
  deliveryDate?: string,
  sellerId?: number,
  categoryId?: number,
  matchBy?: string,
  parentCategoryId?: number,
  lat?: number,
  long?: number
): Promise<ApiResponse<ItemOptionsData>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const params: {
    deliveryDate?: string;
    sellerId?: number;
    categoryId?: number;
    matchBy?: string;
    categoryParentId?: number;
    lat?: number;
    long?: number;
  } = {};

  if (deliveryDate) {
    params.deliveryDate = deliveryDate;
  }

  if (sellerId && sellerId !== undefined) {
    params.sellerId = sellerId;
  }

  if (categoryId) {
    params.categoryId = categoryId;
  }
  if (matchBy) {
    params.matchBy = matchBy;
  }

  if (parentCategoryId) {
    params.categoryParentId = parentCategoryId;
  }
  
  if (lat) {
    params.lat = lat;
  }
  if (long) {
    params.long = long;
  }

  let url = getApiUrl(
    "/itemoptions",
    undefined,
    params,
    hasSubdomain ? domain : undefined
  );

  if (mobileNumber) {
    url = getApiUrl(
      `/buyer/itemoptions/cmobile/${mobileNumber}`,
      undefined,
      params,
      undefined
    );
  }

  try {
    const response = await apiRequest<ItemOptionsData>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      // response.data.availableItems = response.data.availableItems.map(
      //   (item, index) => {
      //     return {
      //       ...item,
      //       soldout: index % 2 === 0
      //     };
      //   }
      // );
      return response;
    } else {
      throw new Error("No item options found");
    }
  } catch (error) {
    console.error("Error fetching item options:", error);
    throw error;
  }
}

export async function getItemOptionsAPIWithNetworkId(
  mobileNumber: string | null,
  request: Request,
  deliveryDate?: string,
  sellerId?: number
): Promise<ApiResponse<ItemOptionsData>> {
  // const { domain, hasSubdomain } = getDomainFromRequest(request);
  const params: { deliveryDate?: string; sellerId?: number; buyerId: number } =
    { buyerId: 1633 };

  if (deliveryDate) {
    params.deliveryDate = deliveryDate;
  }

  if (sellerId) {
    params.sellerId = sellerId;
  }

  const url = getApiUrl(
    `/buyer/${6}/itemoptions`,
    undefined,
    params,
    undefined
  );

  try {
    const response = await apiRequest<ItemOptionsData>(
      url,
      "GET",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("No item options found");
    }
  } catch (error) {
    console.error("Error fetching item options:", error);
    throw error;
  }
}

/**
 * Precheck Order API
 * @param payload - The payload containing order details
 * @param access_token - The authentication token
 * @returns A promise that resolves to PrecheckOrderResponse
 */
export async function precheckOrderAPI(
  payload: PrecheckOrderPayload,
  request: Request
): Promise<ApiResponse<PrecheckOrderResponse>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const params = {};
  const url = getApiUrl(
    "/precheckorder",
    undefined,
    params,
    hasSubdomain ? domain : undefined
  );

  // const url =
  //   "https://6bfe032c-8f37-477f-bb19-bf17621c9d3e.mock.pstmn.io/buyer/d/restaurant.mnetlive.com/precheckorder";

  try {
    const response = await apiRequest<PrecheckOrderResponse>(
      url,
      "POST",
      payload,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Precheck order failed: " + JSON.stringify(response));
    }
  } catch (error) {
    console.error("Error prechecking order: ", error);
    throw error;
  }
}

export async function precheckAndConfirmOrderAPI(
  payload: PrecheckOrderPayload,
  request: Request
): Promise<ApiResponse<ConfirmOrderResponse>> {
  // const { domain, hasSubdomain } = getDomainFromRequest(request);
  const params = {};
  const url = getApiUrl(
    "/buyer/precheckandconfirm",
    undefined,
    params,
    undefined
  );

  try {
    const response = await apiRequest<ConfirmOrderResponse>(
      url,
      "POST",
      payload,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error(
        "Precheck and confirm order failed: " + JSON.stringify(response)
      );
    }
  } catch (error) {
    console.error("Error prechecking and confirm order: ", error);
    throw error;
  }
}

/**
 * Confirm Order API
 * @param payload - The payload containing confirmed order details
 * @param access_token - The authentication token
 * @returns A promise that resolves to ConfirmOrderResponse
 */
//TODO:GG Check networkId instead of buyerId ??
export async function confirmOrderAPI(
  payload: ConfirmOrderPayload,
  request: Request
): Promise<ApiResponse<MnetCoreResponse<PrecheckOrderResponse>>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const params = {};
  const url = getApiUrl(
    "/confirmorder" +
      (payload.preconfirmUid ? `?preconfirmuid=${payload.preconfirmUid}` : ""),
    undefined,
    params,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<MnetCoreResponse<PrecheckOrderResponse>>(
      url,
      "POST",
      payload,
      {},
      true,
      request
    );

    return response;
  } catch (error) {
    console.error("Error confirming order:", error);
    throw error;
  }
}

/**
 * Fetches orders based on buyer ID.
 * @param buyerId - The ID of the buyer.
 * @param access_token - The authentication token.
 * @param request - The incoming request object.
 * @returns A promise that resolves to OrderResponse.
 */
/**
 * Fetches orders based on buyer ID.
 */
export async function getOrdersAPI(
  buyerId: number,
  request: Request
): Promise<ApiResponse<OrderResponse>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const params = { buyerId: buyerId.toString() };
  const url = getApiUrl(
    `/orders/v2`,
    undefined,
    params,
    hasSubdomain ? domain : undefined
  );

  const response = await apiRequest<OrderResponse>(
    url,
    "GET",
    undefined,
    {},
    true,
    request
  );

  if (!response) throw new Error("No orders found");
  return response;
}

export async function getOrderDetailsAPI(
  orderId: string,
  request: Request
): Promise<ApiResponse<Order>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const url = getApiUrl(
    `/orders/details/${orderId}`,
    undefined,
    undefined,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<Order>(
      url,
      "GET",
      null,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Order details api failed");
    }
  } catch (error) {
    console.error("Error getting order details:", error);
    throw error;
  }
}

export async function changeAddressAPI(
  payload: {
    address: string;
    latitude: number;
    longitude: number;
  },
  request: Request
): Promise<ApiResponse<void>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const url = getApiUrl(
    `/address`,
    undefined,
    undefined,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<void>(
      url,
      "PUT",
      payload,
      {},
      true,
      request
    );
    return response;
  } catch (error) {
    console.error("Error changing address:", error);
    throw error;
  }
}

export async function getAddressAPI(
  request: Request
): Promise<ApiResponse<Address>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const url = getApiUrl(
    `/address`,
    undefined,
    undefined,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<Address>(
      url,
      "GET",
      null,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Get address api failed");
    }
  } catch (error) {
    console.error("Error changing address:", error);
    throw error;
  }
}

/**
 * Initiates a payment for an order
 */
export async function initiatePayment(
  orderGroupId: number,
  payload: InitiatePayment,
  request: Request
): Promise<ApiResponse<MoneyCollectionDetails>> {
  let url;
  if (fNodeEnv === "development") {
    url = MOCK_API_BASE_URL + `/upipayment/order/11/deposit`;
  } else {
    url = getApiUrl(
      `/mc/order/${orderGroupId}/deposit`,
      undefined,
      undefined,
      undefined
    );
  }

  try {
    const response = await apiRequest<MoneyCollectionDetails>(
      url,
      "POST",
      payload,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to initiate payment");
    }
  } catch (error) {
    console.error("Error initiating payment:", error);
    throw error;
  }
}

/**
 * Initiates a deposit payment using the preconfirm unique ID
 */
export async function initiateDeposit(
  preconfirmUid: string,
  amount: number,
  request: Request
): Promise<ApiResponse<MnetCoreResponse<MoneyCollectionDetails>>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const url = getApiUrl(
    `/initiatedeposit/${preconfirmUid}/${amount}`,
    undefined,
    {},
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<MnetCoreResponse<MoneyCollectionDetails>>(
      url,
      "POST",
      undefined,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Failed to initiate deposit");
    }
  } catch (error) {
    console.error("Error initiating deposit:", error);
    throw error;
  }
}

/**
 * Gets the status of a UPI payment.
 */
export async function getUpiPaymentStatus(
  refId: number,
  request: Request
): Promise<ApiResponse<MoneyCollectionDetails>> {
  let url;
  if (fNodeEnv === "development") {
    url = MOCK_API_BASE_URL + `/upipayment/deposit/${refId}/updatestatus`;
  } else {
    url = getApiUrl(
      `/upipayment/deposit/${refId}/updatestatus`,
      undefined,
      undefined,
      undefined
    );
  }

  try {
    const response = await apiRequest<MoneyCollectionDetails>(
      url,
      "PUT",
      null,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Initiate payment request failed");
    }
  } catch (error) {
    console.error("Error Initiate payment:", error);
    throw error;
  }
}

export async function getUpiPaymentStatusDetails(
  refId: string | number,
  request: Request
): Promise<ApiResponse<UpiPaymentStatusDetails>> {
  let url;
  if (fNodeEnv === "development") {
    url =
      MOCK_API_BASE_URL + `/upipayment/deposit/${refId}/updatestatus/details`;
  } else {
    url = getApiUrl(
      `/mc/deposit/${refId}/updatestatus/details`,
      undefined,
      undefined,
      undefined
    );
  }

  try {
    const response = await apiRequest<UpiPaymentStatusDetails>(
      url,
      "PUT",
      null,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Initiate payment request failed");
    }
  } catch (error) {
    console.error("Error Initiate payment:", error);
    throw error;
  }
}

export async function getWalletDetails(
  userId: number,
  access_token: string,
  request: Request
): Promise<ApiResponse<WalletDetails>> {
  const { domain } = getDomainFromRequest(request);
  const url = getApiUrl(`/buyer/d/${domain}/mywallet`);

  try {
    const response = await apiRequest<WalletDetails>(
      url,
      "GET",
      null,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Get Wallet Details request failed");
    }
  } catch (error) {
    console.error("Error Get Wallet Details", error);
    throw error;
  }
}

export async function getWalletRecentTransactions(
  userId: number,
  access_token: string,
  request: Request
): Promise<ApiResponse<Transaction[]>> {
  const { domain } = getDomainFromRequest(request);
  const url = getApiUrl(`/buyer/d/${domain}/recentwallettransactions`);
  try {
    const response = await apiRequest<Transaction[]>(
      url,
      "GET",
      null,
      {},
      true,
      request
    );

    if (response) {
      return response;
    } else {
      throw new Error("Get Wallet Details request failed");
    }
  } catch (error) {
    console.error("Error Get Wallet Details", error);
    throw error;
  }
}

export async function getLanguage(
  request: Request
): Promise<ApiResponse<string>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const url = getApiUrl(
    `/language`,
    undefined,
    undefined,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<string>(
      url,
      "GET",
      null,
      {},
      false,
      request
    );
    if (response.data === "" || response) {
      return response;
    } else {
      throw new Error("Get Language api failed");
    }
  } catch (error) {
    console.error("Error Get language:", error);
    throw error;
  }
}

export async function updateLanguage(
  languageCode: string,
  request: Request
): Promise<ApiResponse<string>> {
  const { domain, hasSubdomain } = getDomainFromRequest(request);
  const url = getApiUrl(
    `/language`,
    undefined,
    undefined,
    hasSubdomain ? domain : undefined
  );

  try {
    const response = await apiRequest<string>(
      url,
      "PUT",
      { language: languageCode },
      {},
      false,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("change language api failed");
    }
  } catch (error) {
    console.error("Error change language:", error);
    throw error;
  }
}

export async function getAllSupportTickets(
  buyerId: number,
  request: Request
): Promise<ApiResponse<SupportTicket[]>> {
  const url = getApiUrl(
    `/support/tickets?userId=${buyerId}`,
    undefined,
    undefined,
    undefined
  );

  try {
    const response = await apiRequest<SupportTicket[]>(
      url,
      "GET",
      null,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Get Support tickets api failed");
    }
  } catch (error) {
    console.error("Error Get Support tickets:", error);
    throw error;
  }
}

export async function createSupportTicket(
  payload: {
    userId: number;
    // appSellerId: number;
    ticketType: string;
    status: string;
    requestedCallBack: boolean;
    description: string | "";
    orderGroupId?: number;
    sellerId?: number;
    sellerName?: string;
  },
  request: Request
): Promise<ApiResponse<SupportTicket>> {
  const url = getApiUrl(
    "/support/create_ticket",
    undefined,
    undefined,
    undefined
  );

  try {
    const response = await apiRequest<SupportTicket>(
      url,
      "POST",
      payload,
      {},
      true,
      request
    );
    if (response) {
      return response;
    } else {
      throw new Error("Create support ticket api failed");
    }
  } catch (error) {
    console.error("Error create support ticket api:", error);
    throw error;
  }
}

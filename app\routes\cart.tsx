import React, { useEffect, useState, useCallback } from "react";
import {
  ActionFunction,
  json,
  LoaderFunction,
  redirect
} from "@remix-run/node";
import {
  useNavigate,
  useFetcher,
  useLoaderData,
  useLocation
} from "@remix-run/react";
// import dayjs from "dayjs";

import { confirmOrderAPI } from "@services/buyer.service";
import type { Cart } from "~/types";
import {
  ConfirmOrderPayload,
  PrecheckOrderResponse,
  SellerOrderItem,
  User
} from "~/types";
import { getSession } from "~/utils/session.server"; // Ensure this is correctly imported

import Button from "~/components/Button";
import SellerOrdersCard from "~/components/SellerOrdersCard";
import SuccessDialog from "~/components/SuccessDialog"; // Import the SuccessDialog component
import Toast from "~/components/Toast"; // Add Toast import
import { roundOff } from "~/utils/roundOff"; // Ensure this utility exists
import { BackNavHeader } from "~/components/BackNavHeader";
import { createClientResponse, requireAuth } from "~/utils/clientReponse";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { parseJWT } from "~/utils/token-utils";
// import { Address, DecodedToken } from "~/types/user";
import { handleWhatsappClick } from "~/components/WhatsappCTA";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import { ChevronRight, ShoppingCart, MapPin } from "lucide-react";
import { isEmptyNullOrUndefinedString } from "~/utils/string";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { useCartStore } from "~/stores/cart.store";
import MoQPopup from "~/components/MoQPopup";
import { DecodedToken } from "~/types/user";
import TruncatedText from "~/components/TruncatedText";
import { MnetCoreResponse } from "~/types/Api";
import { cn } from "~/utils/cn";
// import ItemDetails from "~/components/chooseitem/ItemDetails";
import { useRequireAuth } from "~/hooks/useRequireAuth";
interface ActionData {
  success?: boolean;
  error?: string;
}

interface LoaderData {
  // address: Address;
  approxPricing: boolean;
  mobileNumber?: string;
}

export const loader: LoaderFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  if (!access_token) {
    return redirect("/login");
  }

  try {
    const decoded = parseJWT(access_token) as DecodedToken;

    if (!decoded || !decoded.userDetails) {
      return redirect("/login");
    }

    const url = new URL(request.url);
    const approxPricingStr = url.searchParams.get("approxPricing");
    let approxPricing = false;
    if (approxPricingStr && approxPricingStr == "true") {
      approxPricing = true;
    }
    // const response = await getAddressAPI(request);
    // const existingAddress = response.data;

    // return createClientResponse<LoaderData, Address>(
    //   request,
    //   {
    //     approxPricing,
    //     mobileNumber: decoded?.userDetails?.mobileNumber,
    //     // address: existingAddress
    //   },
    //   // response
    // );
    return {
      approxPricing,
      mobileNumber: decoded?.userDetails?.mobileNumber
    };
  } catch (err) {
    console.error("Error decoding access_token:", err);
    return redirect("/login");
  }
};

export const action: ActionFunction = async ({ request }) => {
  const session = await getSession(request.headers.get("Cookie"));
  const access_token = session.get("access_token") as string | null;
  const user: User | null = session.get("user") as User | null;

  const auth = await requireAuth(request, "", false);
  if (auth && auth.authRequired) {
    return json(auth);
  }

  if (!access_token || !user) {
    return json<ActionData>({ error: "Unauthorized" }, { status: 401 });
  }

  const formData = await request.formData();
  const orderData = formData.get("order");

  if (!orderData || typeof orderData !== "string") {
    return json<ActionData>({ error: "Invalid order data" }, { status: 400 });
  }

  let order: PrecheckOrderResponse;
  try {
    order = JSON.parse(orderData);
  } catch (error) {
    console.error("Error parsing order data:", error);
    return json<ActionData>(
      { error: "Invalid order data format" },
      { status: 400 }
    );
  }

  // Prepare the payload for confirmOrderAPI
  const payload: ConfirmOrderPayload = {
    sellerInventoryId: order.sellerInventoryId,
    buyerId: order.buyerId,
    codSelected: order.codAllowed,
    codAllowed: order.codAllowed,
    codAmount: order.codAmount,
    deliveryDate: order.deliveryDate,
    deliveryCharges: order.deliveryCharges,
    discountAmount: order.discountAmount,
    walletAmount: order.walletAmount,
    existingOrderGroupId: order.existingOrderGroupId,
    cartKey: order.cartKey,
    items: order.items.map((item) => ({
      sellerId: item.sellerId,
      inventoryId: item.inventoryId,
      sellerItemId: item.sellerItemId,
      pricePerUnit: item.pricePerUnit,
      quantity: item.quantity,
      amount: item.amount
    })),
    preconfirmUid: order.preconfirmUid
  };

  try {
    console.log("Confirming order with payload:", JSON.stringify(payload));

    const response = await confirmOrderAPI(payload, request);
    console.log("Order confirmed successfully.");

    if (!response.data.success) {
      return json<ActionData>(
        { error: response.data.error?.message },
        { status: 500 }
      );
    }

    return createClientResponse<
      ActionData,
      MnetCoreResponse<PrecheckOrderResponse>
    >(request, { success: true }, response);
  } catch (error) {
    console.error("Error confirming order:", error);
    return json<ActionData>(
      { error: "Failed to confirm order" },
      { status: 500 }
    );
  }
};

// Add this component before the main Cart component
const EmptyCart: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center justify-center h-[80vh] px-4">
      <ShoppingCart className="w-16 h-16 text-neutral-400 mb-4" />
      <h2 className="text-xl font-semibold text-typography-800 mb-2">
        Your cart is empty
      </h2>
      <p className="text-sm text-typography-500 text-center mb-6">
        {`Looks like you haven't added any items to your cart yet.`}
      </p>
      <Button
        onClick={() => navigate("/chooseitems")}
        className="p-3 rounded-xl flex justify-between align-center gap-2 bg-primary hover:bg-primary-600 text-white"
      >
        <span>Start Shopping</span>
        <ChevronRight className="self-center" size={"1.25rem"} />
      </Button>
    </div>
  );
};

const Cart: React.FC = () => {
  const navigate = useNavigate();
  const fetcher = useFetcher<ActionData>();
  const precheckFetcher = useFetcher();
  const loader = useLoaderData<LoaderData>();
  const location = useLocation();
  const {
    cart: currentCart,
    clearCart,
    setCart
  } = useCartStore((state) => state);

  useRequireAuth();

  // Initialize order state from location or localStorage
  const [order, setOrder] = useState<PrecheckOrderResponse | null>(() => {
    const locationOrder = location.state?.order as PrecheckOrderResponse;
    if (locationOrder) {
      // Store in localStorage when available from location
      localStorage.setItem("currentOrder", JSON.stringify(locationOrder));
      return locationOrder;
    }

    // Try to get from localStorage if not in location state
    const savedOrder = localStorage.getItem("currentOrder");
    return savedOrder ? JSON.parse(savedOrder) : null;
  });

  const [placedOrder, setPlacedOrder] = useState<PrecheckOrderResponse | null>(
    null
  );
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [isSubmittingOrder, setIsSubmittingOrder] = useState(false);
  const { appSource, networkConfig } = useAppConfigStore((state) => state);
  const { itemOptionsData, selectedParentCategory, categoryTypeList } =
    chooseitemsStore((state) => state);
  const [showLoader, setShowLoader] = useState(true);
  const [showMoq, setShowMoq] = useState(false);
  const [showMov, setShowMov] = useState(false);
  const [showServiceAreaToast, setShowServiceAreaToast] = useState(false);

  const precheckItemMap = new Map<number, SellerOrderItem>();
  order?.items?.forEach((item) => {
    precheckItemMap.set(item.sellerItemId, item);
  });

  // Add effect to handle return from address page
  useEffect(() => {
    const isReturningFromAddress = location.state?.fromAddress;
    if (isReturningFromAddress && order) {
      // Clear the state flag
      window.history.replaceState(
        { ...window.history.state, fromAddress: undefined },
        ""
      );

      // Trigger precheck API
      handleItemUpdate();
    }
  }, [location.state]);

  const checkMoqMov = () => {
    if (order?.insufficientOrderQuantity) {
      setShowMoq(true);
      setShowMov(false);
      return true;
    } else if (order?.insufficientOrderValue) {
      setShowMov(true);
      setShowMoq(false);
      return true;
    } else {
      setShowMoq(false);
      setShowMov(false);
      return false;
    }
  };

  // Update localStorage when order changes
  useEffect(() => {
    if (order) {
      localStorage.setItem("currentOrder", JSON.stringify(order));
    } else {
      localStorage.removeItem("currentOrder");
    }
  }, [order]);

  // Initialize cart from order on component mount
  useEffect(() => {
    if (order && order.items) {
      const initialCart: Cart = {};
      order.items.forEach((item) => {
        if (item.quantity > 0) {
          initialCart[item.sellerItemId] = {
            itemId: item.sellerItemId,
            qty: item.quantity,
            amount: item.amount,
            cartKey: order.cartKey
          };
        }
      });

      // Store cart in localStorage
      // localStorage.setItem(
      //   `cart-${order.cartKey}`,
      //   JSON.stringify(initialCart)
      // );
      localStorage.setItem("cartKey", order.cartKey || "");

      setCart(initialCart, order.cartKey);
    } else {
      // Try to restore cart from localStorage if order is not available
      const savedCartKey = localStorage.getItem("cartKey");
      const savedCart = localStorage.getItem(`cart_${savedCartKey}`);

      if (savedCart && savedCartKey) {
        const parsedCart = JSON.parse(savedCart);
        setCart(parsedCart, savedCartKey);
      }
    }
  }, []);

  // Clear localStorage when order is successfully placed
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data?.success) {
      localStorage.removeItem("currentOrder");
      localStorage.removeItem("currentCart");
      localStorage.removeItem("cartKey");
    }
  }, [fetcher.state, fetcher.data]);

  // Update order and cart when precheck response is received
  useEffect(() => {
    if (precheckFetcher.data && precheckFetcher.state === "idle") {
      try {
        const newOrder = precheckFetcher.data as PrecheckOrderResponse;
        if (newOrder) {
          setOrder(newOrder);

          // Create new cart object based on precheck response
          const updatedCart: Cart = {};
          newOrder.items.forEach((item) => {
            if (item.quantity > 0) {
              updatedCart[item.sellerItemId] = {
                itemId: item.sellerItemId,
                qty: item.quantity,
                amount: item.amount,
                cartKey: newOrder.cartKey
              };
            }
          });

          // Update cart store with new cart data
          setCart(updatedCart, newOrder.cartKey);
        }
      } catch (error) {
        console.error("Error updating order:", error);
        setErrorMessage("Failed to update order. Please try again.");
      }
    }
  }, [precheckFetcher.state, precheckFetcher.data]);

  // Handle item updates and trigger precheck API
  const handleItemUpdate = async () => {
    if (!order?.items || !order.cartKey) {
      console.error("Order or items not available");
      return;
    }

    try {
      // Get the current cart state directly from the store
      const cart = useCartStore.getState().cart;

      // Submit precheck request
      precheckFetcher.submit(
        {
          intent: "precheck",
          cart: JSON.stringify(cart),
          deliveryDate: order.deliveryDate,
          sellerId: order.sellerId,
          sellerDataId: order.sellerInventoryId,
          codAllowed: order.codSelected,
          existingOrderGroupId: order.existingOrderGroupId,
          cartKey: order.cartKey,
          preconfirmUid: order.preconfirmUid
        },
        { method: "post", action: "/chooseitems" }
      );
    } catch (error) {
      console.error("Error updating cart:", error);
      setErrorMessage("Failed to update cart. Please try again.");
    }
  };

  // Handle actionData responses
  useEffect(() => {
    if (fetcher.state === "submitting") {
      setIsSubmittingOrder(true);
    }

    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.error) {
        setErrorMessage(fetcher.data.error);
        setShowServiceAreaToast(true);
        setIsSubmittingOrder(false);
      } else if (fetcher.data.success) {
        if (order) {
          // Capture the placed order details before clearing
          setPlacedOrder(order);

          // Clear the current order and cart
          localStorage.removeItem("order");
          clearCart(order.cartKey);
          setOrder(null);

          // Show success dialog
          setShowSuccessDialog(true);
          // Only reset submission state after dialog is shown
          setIsSubmittingOrder(false);
        } else {
          // Edge case: success but no order in state
          console.warn("Order data missing after successful confirmation.");
          setIsSubmittingOrder(false);
        }
      }
    }
  }, [fetcher.state, fetcher.data, order]);

  // Add new useEffect for loader timing
  useEffect(() => {
    if (!order && !placedOrder) {
      const timer = setTimeout(() => {
        setShowLoader(false);
      }, 30000);
      return () => clearTimeout(timer);
    }
  }, [order, placedOrder]);

  const newOrderItems = useCallback(
    () =>
      order?.items.filter((item) => {
        const currentQty = currentCart[item.sellerItemId]?.qty || 0;
        const orderedQty = item.availableCartItem?.orderedQty || 0;
        return (
          (!item.isSoldOut || item.quantity > 0) && currentQty > orderedQty
        );
      }),
    [order?.items, currentCart]
  );

  const disablePlaceOrderButton = useCallback(() => {
    return (
      newOrderItems()?.length === 0 ||
      isSubmittingOrder ||
      fetcher.state === "submitting" ||
      !order?.defaultAddress?.buyerInServiceArea
    );
  }, [newOrderItems, isSubmittingOrder, fetcher.state]);

  const handlePlaceOrder = () => {
    if (!order) {
      setErrorMessage("No order data available.");
      return;
    }

    if (!order?.defaultAddress?.buyerInServiceArea) {
      setShowServiceAreaToast(true);
      return;
    }

    if (checkMoqMov()) {
      return;
    }

    if (isSubmittingOrder) {
      return;
    }

    // filter the sold out items with zero quantity
    const filteredOrder = {
      ...order,
      items: order.items.filter(
        (item) => item.quantity > 0 && item.isSoldOut === false
      )
    };

    // Submit the order via fetcher.submit
    fetcher.submit(
      { order: JSON.stringify(filteredOrder) },
      { method: "post", action: "/cart" }
    );
  };

  const handleWhatsappRedirect = () => {
    handleWhatsappClick(networkConfig?.wabMobileNumber || "", "Track order");
  };

  const handleRedirect = () => {
    setShowSuccessDialog(false);
    navigate("/home/<USER>", { replace: true });
  };

  // Replace the existing no-order check with this new logic

  // console.log(
  //   "selectedParentCategory",
  //   selectedParentCategory,
  //   itemOptionsData?.sellerId
  // );

  const handleBack = () => {
    if (
      itemOptionsData?.availableItems &&
      (categoryTypeList.includes("L2") || categoryTypeList.includes("L3"))
    ) {
      navigate(
        `/chooseitems?sellerId=${itemOptionsData.sellerId}&deliveryDate=${itemOptionsData.deliveryDate}&categoryId=${selectedParentCategory?.id}`,
        { replace: true }
      );
    } else {
      navigate(
        `/chooseitems?sellerId=${itemOptionsData?.sellerId}&deliveryDate=${itemOptionsData?.deliveryDate}`,
        { replace: true }
      );
    }
  };

  // Add this check right after the BackNavHeader
  const isCartEmpty = !order?.items || order.items.length === 0;

  // show the address btn
  const showAddressBtn =
    (order?.defaultAddress &&
      isEmptyNullOrUndefinedString(order?.defaultAddress.address || "")) ||
    (parseInt(order?.defaultAddress?.latitude || "0") === 0 &&
      parseInt(order?.defaultAddress?.longitude || "0") === 0);

  return (
    <div className="flex flex-col h-screen bg-neutral-100 fixed inset-0">
      {/* Header Section */}
      <BackNavHeader buttonText="Confirm Order" handleBack={handleBack} />

      {/* Show empty cart state when cart is empty */}
      {isCartEmpty ? (
        <EmptyCart />
      ) : (
        <>
          {/* Show loader while fetching initial data */}
          {!order && !placedOrder && (
            <>
              {showLoader ? (
                <SpinnerLoader size={12} loading={true} />
              ) : (
                <div className="flex items-center justify-center h-screen">
                  <div className="p-4 text-red-700 text-center rounded">
                    No order details found. Please try again.
                  </div>
                </div>
              )}
            </>
          )}

          {/* Show loading indicator during precheck */}
          {precheckFetcher.state !== "idle" && (
            <SpinnerLoader size={12} loading={true} />
          )}

          {/* Seller Orders */}
          <div className="flex-grow h-full bg-neutral-100 mb-4">
            {order && (
              <SellerOrdersCard
                approxPricing={loader.approxPricing}
                displayPrices={order.displayPrices}
                orderDetails={order}
                handleBack={handleBack}
                onItemUpdate={handleItemUpdate}
              />
            )}
          </div>

          {/* Error Message */}
          {errorMessage && (
            <div className="p-4 bg-red-100 text-red-700 text-center">
              {errorMessage}
            </div>
          )}

          {/* PLACE ORDER Button */}
          {order && (
            <div className="fixed w-full bottom-0 right-0 left-0 flex flex-col items-center justify-between bg-white shadow-md">
              {/* approx pricing banner */}
              {order.insufficientOrderQuantity && (
                <div className="w-full rounded-t-lg bg-secondary-50 h-12 flex justify-center items-center px-2">
                  <p className="text-xs font-normal text-secondary text-center">
                    Online ordering for
                    <span className="font-semibold"> {order?.sellerName} </span>
                    is only valid for orders
                    <span className="font-semibold">
                      {" "}
                      above {order?.minOrderQty} kg.{" "}
                    </span>
                    <br />
                    Please add atleast{" "}
                    <span className="font-semibold">
                      {" "}
                      {(order?.minOrderQty || 0).toFixed(2)} kg{" "}
                    </span>{" "}
                    more to proceed.
                  </p>
                </div>
              )}
              {order.insufficientOrderValue && (
                <div className="w-full rounded-t-lg bg-secondary-50 h-12 flex justify-center items-center px-2">
                  <p className="text-xs font-normal text-secondary text-center">
                    <span className="font-semibold"> {order?.sellerName} </span>
                    requires a minimum order of
                    <span className="font-semibold">
                      {" "}
                      above ₹{order?.minOrderValue}{" "}
                    </span>{" "}
                    for online orders. <br />
                    Please add at least{" "}
                    <span className="font-semibold">
                      {" "}
                      ₹{(order?.minOrderValue - order.totalAmount).toFixed(
                        2
                      )}{" "}
                    </span>{" "}
                    more to proceed.
                  </p>
                </div>
              )}
              {loader.approxPricing && (
                <div className="w-full rounded-t-lg bg-secondary-50 h-12 flex justify-center items-center px-2">
                  <p className="text-xs font-normal text-secondary text-center">
                    Please note these are
                    <span className="font-semibold"> approximate rates </span>
                    from {order?.sellerName}
                    <br /> the
                    <span className="font-semibold"> final amount </span>
                    to be paid will be updated soon.
                  </p>
                </div>
              )}
              {order?.defaultAddress && showAddressBtn ? (
                <Button
                  className="my-4 p-3 rounded-xl w-11/12 bg-primary hover:bg-primary-600 text-white"
                  onClick={() =>
                    navigate("/changeaddress?redirectTo=/cart", {
                      state: {
                        address: order?.defaultAddress,
                        isEdit: true,
                        from: "cart",
                        returnTo: "/cart"
                      }
                    })
                  }
                >
                  Confirm address
                </Button>
              ) : (
                <div className="flex flex-col items-center justify-between p-4 bg-white shadow-md w-full">
                  <div
                    className={cn(
                      "flex flex-col w-full gap-2 justify-between mb-3 border-b border-neutral-200 pb-3",
                      !order.defaultAddress.buyerInServiceArea && "text-red-500"
                    )}
                  >
                    <div className="flex items-center w-full gap-2 justify-between">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-primary" />
                        <div className="flex gap-1">
                          {order?.defaultAddress?.name && (
                            <div
                              className={cn(
                                "flex gap-1 text-xs tracking-wide",
                                !order.defaultAddress.buyerInServiceArea &&
                                  "text-red-500 font-bold bg-orange-50 px-2 py-1 rounded-md"
                              )}
                            >
                              <span>
                                {order.defaultAddress.buyerInServiceArea
                                  ? "Delivery at "
                                  : "Not Deliverable at "}
                              </span>
                              <span className="font-semibold">
                                {order.defaultAddress.name.toLocaleUpperCase()}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      <Button
                        className="text-primary text-xs"
                        type="button"
                        onClick={() =>
                          navigate(
                            "/select-address?flowType=select-address&returnTo=/cart",
                            {
                              state: {
                                from: "cart",
                                returnTo: "/cart",
                                flowType: "select-address"
                              }
                            }
                          )
                        }
                      >
                        CHANGE
                      </Button>
                    </div>
                    <TruncatedText
                      className={cn(
                        "text-xs font-light tracking-wide text-typography-400 w-[80vw]",
                        !order.defaultAddress.buyerInServiceArea &&
                          "text-red-500"
                      )}
                      text={order?.defaultAddress.address}
                    />
                  </div>
                  <div className="flex items-center w-full gap-2 justify-between">
                    <div className="flex flex-col gap-1 w-[50vw]">
                      <div className="flex gap-2 items-center object-center ">
                        <div className="flex align-center item-center justify-center border rounded border-neutral-400 p-0.5 w-8 h-6">
                          <img src="/cash_icon.svg" alt="" />
                        </div>
                        <p className="text-xs font-light tracking-wide text-typography-500 self-center">
                          PAY USING
                        </p>
                      </div>
                      <div className="text-sm font-light tracking-wide text-typography-800 self-start">
                        {" "}
                        Cash on Delivery{" "}
                      </div>
                    </div>

                    <Button
                      type="button"
                      onClick={handlePlaceOrder}
                      disabled={disablePlaceOrderButton()}
                      className={`p-3 rounded-xl flex justify-between align-center gap-1 w-full  ${
                        disablePlaceOrderButton()
                          ? "bg-neutral-500 text-neutral-800 cursor-not-allowed"
                          : "bg-primary hover:bg-primary-600 text-white"
                      }`}
                    >
                      {order.displayPrices !== false ? (
                        <div className="flex flex-col gap-1 self-center">
                          <p
                            className={`text-xs font-medium whitespace-nowrap
                              ${
                                disablePlaceOrderButton()
                                  ? "text-neutral-800"
                                  : "text-white"
                              }
                              self-start`}
                          >
                            <span>{`₹ ${roundOff(
                              order.totalAmount,
                              true
                            )}`}</span>
                          </p>
                          <p
                            className={`text-[0.625rem] font-light tracking-wide whitespace-nowrap
                              ${
                                disablePlaceOrderButton()
                                  ? "text-neutral-800"
                                  : "text-white"
                              }
                              self-start`}
                          >
                            <span>TOTAL </span>
                          </p>
                        </div>
                      ) : (
                        <div className="flex flex-col gap-1 w-10 h-8"></div>
                      )}
                      <div className="flex gap-2 self-center justify-end">
                        <span className=" flex flex-wrap w-fit text-right tracking-wider">
                          {isSubmittingOrder ||
                          fetcher.state === "submitting" ? (
                            <span className="flex items-center gap-1">
                              {" "}
                              <div className="border-2 border-gray-400 rounded-full w-4 h-4 border-t-transparent animate-spin "></div>
                              PLACING
                            </span>
                          ) : (
                            "PLACE ORDER"
                          )}
                        </span>
                        <ChevronRight
                          className="self-center"
                          size={"1.25rem"}
                        />
                      </div>
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </>
      )}

      {/* Success Dialog */}
      {showSuccessDialog && placedOrder && (
        <div className="p-16">
          <SuccessDialog
            title="Order Placed"
            message="Thankyou for placing the order"
            buttonText="Okay!"
            buttonType="primary"
            onRedirect={
              appSource === "whatsappchat"
                ? handleWhatsappRedirect
                : handleRedirect
            }
            countdownStart={5} // Set countdown to 5 seconds
          />
        </div>
      )}

      {/* MOQ/MOV Popup */}
      <MoQPopup
        visible={Boolean(
          order?.items && order?.items.length > 0 && (showMoq || showMov)
        )}
        onClose={() => {
          setShowMoq(false);
          setShowMov(false);
        }}
        qty={order?.minOrderQty || 0}
        currentQty={Object.values(currentCart).reduce((acc, item) => {
          const itemDetails = precheckItemMap.get(item.itemId);
          if (itemDetails && itemDetails.weightFactor) {
            return acc + item.qty * itemDetails.weightFactor;
          }
          return acc + item.qty;
        }, 0)}
        value={order?.minOrderValue || 0}
        showMoq={showMoq}
        showMov={showMov}
      />

      {/* Add Toast component */}
      {showServiceAreaToast && (
        <Toast
          type="itemLimited"
          message={
            errorMessage ||
            `${order?.sellerName} is not available at your location.`
          }
          onClose={() => setShowServiceAreaToast(false)}
          duration={3000}
          position="bottom-center"
          width="full"
        />
      )}
    </div>
  );
};

export default Cart;

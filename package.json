{"name": "buyer-web", "private": true, "sideEffects": false, "type": "module", "scripts": {"build:server": "tsc -p tsconfig.server.json", "build": "remix vite:build && echo 'Build process completed' && ls -R build", "dev": "node --inspect ./server.js", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "cross-env NODE_ENV=production node ./server.js", "typecheck": "tsc"}, "dependencies": {"@headlessui/react": "^2.1.8", "@heroicons/react": "^2.2.0", "@lottiefiles/dotlottie-react": "^0.13.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-slot": "^1.2.0", "@react-google-maps/api": "^2.20.3", "@remix-run/express": "^2.12.0", "@remix-run/node": "^2.12.0", "@remix-run/react": "^2.12.0", "@types/jsonwebtoken": "^9.0.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.7.4", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "dompurify": "^3.2.3", "dotenv": "^16.4.5", "express": "^4.19.2", "fuse.js": "^7.1.0", "html-react-parser": "^5.2.2", "isbot": "^4.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.452.0", "morgan": "^1.10.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.4.0", "react-select": "^5.9.0", "react-speech-recognition": "^4.0.0", "react-to-pdf": "^2.0.0", "sonner": "^2.0.5", "swiper": "^11.1.14", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "ua-parser-js": "^2.0.3", "zustand": "^5.0.2"}, "devDependencies": {"@remix-run/dev": "^2.12.0", "@types/compression": "^1.7.5", "@types/express": "^4.17.20", "@types/lodash": "^4.17.13", "@types/morgan": "^1.9.9", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/react-speech-recognition": "^3.9.6", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.19", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.17", "typescript": "^5.1.6", "vite": "^5.1.0", "vite-tsconfig-paths": "^4.2.1"}, "engines": {"node": ">=20.0.0"}}
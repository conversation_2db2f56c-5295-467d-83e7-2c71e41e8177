import React, { useEffect, useRef, useState } from "react";
import {
  AvailableItem,
  Cart,
  ImageViewType,
  ItemCategoryDtos,
  ItemOptionsData
} from "~/types";
import { CategoryListL2 } from "./CategoryListL2";
import { ItemList } from "./ItemList";
import EmptySearchResult from "./EmptySearchResult";
import { CategoryList } from "./CategoryList";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import lodash from "lodash";
import { NetworkAsset } from "../NetworkAssests";

interface CategoryItemListProps {
  data: ItemOptionsData;
  cart: Cart;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  searchStr: string;
  imageViewType: ImageViewType;
  handleSelectL1: (categoryId: number) => void;
  allowScrolling?: boolean;
}

const CategoryItemList: React.FC<CategoryItemListProps> = ({
  data,
  cart,
  onAddItem,
  onRemoveItem,
  searchStr,
  imageViewType,
  handleSelectL1,
  allowScrolling = true
}) => {
  const {
    categoryType,
    setCategoryType,
    selectedCategory,
    loading,
    setSelectedCategory,
    selectedParentCategory,
    setSelectedParentCategory,
    setCategoryTypeList
  } = chooseitemsStore((state) => state);

  const [selectedCategoryId, setSelectedCategoryId] = useState<number>(
    data?.itemCategoryDtos?.[0].id
  ); // Stores the selected category ID
  const [filteredItems, setFilteredItems] = useState<AvailableItem[]>(
    data?.availableItems || []
  );
  const [availableItems, setAvailableItems] = useState<AvailableItem[]>(
    data?.availableItems || []
  );
  const [categories, setCategories] = useState<ItemCategoryDtos[]>([]);
  // const fetcher = useFetcher<{ itemOptionsData: ItemOptionsData }>();

  useEffect(() => {
    if (data?.itemCategoryL3Dtos?.length && data?.itemCategoryL2Dtos?.length) {
      setCategoryType("L3");
      setCategoryTypeList("L3");
    } else if (data?.itemCategoryL2Dtos?.length) {
      setCategoryType("L2");
      setCategoryTypeList("L2");
    } else if (data?.itemCategoryDtos?.length) {
      setCategoryType("L1");
      setCategoryTypeList("L1");

      const filteredItems =
        data?.availableItems?.filter((item) =>
          item?.itemCategories?.includes(
            data.selectedL1CategoryId || data.itemCategoryDtos?.[0].id
          )
        ) || [];
      setFilteredItems([...filteredItems]);
      setSelectedCategoryId(
        data.selectedL1CategoryId || data.itemCategoryDtos?.[0].id
      );
    } else {
      setCategoryType("L0");
      setCategoryTypeList("L0");

      // const filteredItems = data?.availableItems.filter((item) =>
      //   item?.itemCategories?.includes(
      //     selectedCategoryId || data.selectedL1CategoryId
      //   )
      // );
      setFilteredItems([...(data?.availableItems || [])]);
    }
  }, [
    data?.itemCategoryL3Dtos,
    data?.itemCategoryL2Dtos,
    data?.itemCategoryDtos,
    data?.availableItems,
    setCategoryTypeList,
    setCategoryType,
    data.selectedL1CategoryId,
    loading
  ]);

  // // add all categories and favItem category
  // // -1 -> All items, -2 -> Favorite
  // useEffect(() => {
  //   if (data?.itemCategoryDtos?.length && data?.availableItems?.length) {
  //     const newItems = data.availableItems
  //       .filter((it) => it.itemCategories?.length)
  //       .map((item) => {
  //         // filter the 0-category items from all items
  //         item?.itemCategories?.push(-1);
  //         // Add only if have freqScore > 0
  //         if (data.favItemsEnabled && item.freqScore > 0) {
  //           item?.itemCategories?.push(-2);
  //         }
  //         return item;
  //       });

  //     setAvailableItems(newItems);
  //     // ?.filter((item) => item.itemCategories?.includes(-1))
  //     setFilteredItems(newItems);
  //     const newCatList = [...data.itemCategoryDtos];

  //     // Add fav item category if enabled
  //     // if (data.favItemsEnabled) {
  //     //   // const favCategory = categories?.find((cat) => cat.id === -2);
  //     //   // if (!favCategory) {
  //     //   newCatList?.unshift({
  //     //     id: -2,
  //     //     name: "Previously Brought",
  //     //     picture: "/fav_items.png"
  //     //   });
  //     //   // setCategories([...categories]);
  //     //   // }
  //     // }

  //     // add all categories
  //     // const allCategories = categories?.find((cat) => cat.id === -1);
  //     // if (!allCategories) {
  //     // newCatList?.unshift({
  //     //   id: -1,
  //     //   name: "All Items",
  //     //   picture: "/all_items.png"
  //     // });
  //     // setCategories([...categories]);
  //     // }
  //     setCategories(newCatList);
  //     // setSelectedCategoryId(newCatList[0].id);
  //     // setSelectedCategory(newCatList[0].id);
  //   } else {
  //     setAvailableItems(data.availableItems || []);
  //     setFilteredItems(data.availableItems || []);
  //   }
  // }, [
  //   categoryType,
  //   data.availableItems,
  //   data.itemCategoryDtos,
  //   loading,
  //   selectedParentCategory?.id
  // ]);

  // filter items by category Id
  useEffect(() => {
    if (searchStr?.length > 3) {
      let filteredItems =
        data?.availableItems?.filter((item) =>
          item?.itemName
            .toLocaleLowerCase()
            .includes(searchStr.toLocaleLowerCase())
        ) || [];

      if (data?.itemCategoryDtos) {
        // const allCategory = categories.find((c) => c.id === -1);
        // const newCatList = data.itemCategoryDtos;
        // if (!allCategory) {
        //   newCatList?.unshift({
        //     id: -1,
        //     name: "All Items",
        //     picture: "/all_items.png"
        //   });
        //   setCategories([...newCatList]);
        // }

        const defaultCategory = data?.itemCategoryDtos?.[0].id;
        // filteredItems = filteredItems.map((item) => {
        //   item.itemCategories?.push(-1);
        //   return item;
        // });

        filteredItems = filteredItems?.filter((item) =>
          item?.itemCategories?.includes(selectedCategoryId || defaultCategory)
        );
        // if (!selectedCategoryId) {
        //   setSelectedCategoryId(-1);
        // }
      }

      setFilteredItems([...filteredItems]);
    }
  }, [
    searchStr,
    data.availableItems,
    selectedCategoryId,
    data.itemCategoryDtos
  ]);

  // Handler for selecting a category
  const handleSelectCategory = (categoryId: number) => {
    if (categoryType !== "L1") {
      handleSelectL1(categoryId);
      // console.log(
      //   "parent",
      //   data?.itemCategoryL2Dtos?.find((cat) => cat.id === categoryId)
      // );
      setSelectedParentCategory(
        data?.itemCategoryL2Dtos?.find((cat) => cat.id === categoryId)
      );

      // // set scroll to false when user change the category
      // setIsScrolled(true);
    } else {
      if (!searchStr?.length) {
        handleSelectL1(categoryId);
      }
      setSelectedCategoryId(categoryId);
      setSelectedCategory(categoryId);
    }
  };

  // console.log(
  //   "categories, availableItems:",
  //   selectedCategory,
  //   selectedCategoryId,
  //   categories,
  //   // availableItems,
  //   filteredItems,
  //   loading,
  //   data
  // );

  return (
    <>
      {data.itemCategoryL3Dtos || data.itemCategoryL2Dtos ? (
        <div className="flex bg-gray-100 pt-2 h-full w-full pb-16 no-scrollbar">
          <CategoryListL2
            categoriesL3={data.itemCategoryL3Dtos}
            categoriesL2={data.itemCategoryL2Dtos}
            selectedCategoryId={selectedCategoryId}
            onSelectCategory={handleSelectCategory}
          />
        </div>
      ) : (
        <ItemWithL1
          data={data}
          cart={cart}
          selectedCategoryId={selectedCategoryId || data.selectedL1CategoryId}
          searchStr={searchStr}
          imageViewType={imageViewType}
          categories={data?.itemCategoryDtos || []}
          filteredItems={
            searchStr.length > 1
              ? (filteredItems || []).filter((item) =>
                  item?.itemName
                    .toLocaleLowerCase()
                    .includes(searchStr?.toLocaleLowerCase() || "")
                )
              : filteredItems
          }
          handleSelectCategory={handleSelectCategory}
          onAddItem={onAddItem}
          onRemoveItem={onRemoveItem}
          allowScrolling={allowScrolling}
        />
      )}
    </>
  );
};

interface ItemWithL1Props {
  data: ItemOptionsData;
  cart: Cart;
  selectedCategoryId?: number;
  searchStr: string;
  imageViewType: ImageViewType;
  categories: ItemCategoryDtos[];
  filteredItems: AvailableItem[];
  handleSelectCategory: (categoryId: number) => void;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  allowScrolling?: boolean;
}

const ItemWithL1: React.FC<ItemWithL1Props> = ({
  data,
  cart,
  selectedCategoryId,
  searchStr,
  imageViewType,
  categories,
  filteredItems,
  handleSelectCategory,
  onAddItem,
  onRemoveItem,
  allowScrolling = true
}) => {
  const { setIsScrolled } = chooseitemsStore((state) => state);

  const scrollableRef = useRef<HTMLDivElement | null>(null);

  // Add effect to reset scroll position when selectedCategoryId changes
  useEffect(() => {
    if (scrollableRef.current) {
      scrollableRef.current.scrollTop = 0;
    }
  }, [selectedCategoryId]);

  useEffect(() => {
    const scrollableElement = scrollableRef.current;

    if (!scrollableElement) return;

    const handleScroll = lodash.throttle(() => {
      const { scrollTop, scrollHeight, clientHeight } = scrollableElement;
      const isScrollable = scrollHeight > clientHeight;

      if (isScrollable) {
        setIsScrolled(scrollTop > 50); // Threshold of 50px
      } else {
        setIsScrolled(false);
      }
    }, 100);

    if (scrollableElement) {
      scrollableElement.addEventListener("scroll", handleScroll);
    }

    return () => {
      if (scrollableElement) {
        scrollableElement.removeEventListener("scroll", handleScroll);
      }
    };
  }, []);

  return (
    <div
      className={`flex bg-gray-100 pt-2 h-full w-full no-scrollbar ${
        allowScrolling ? "overflow-y-auto" : "overflow-y-hidden"
      }`}
    >
      {categories.length ? (
        <CategoryList
          categories={categories}
          selectedCategoryId={selectedCategoryId}
          onSelectCategory={handleSelectCategory}
          allowScrolling={allowScrolling}
        />
      ) : (
        ""
      )}
      <div
        ref={scrollableRef}
        className={`flex flex-col items-center ${
          allowScrolling ? "overflow-y-auto" : "overflow-y-hidden"
        } overflow-x-hidden max-h-full w-full   no-scrollbar`}
      >
        {filteredItems.length > 0 ? (
          <ItemList
            approxPricing={data.approxPricing}
            items={filteredItems}
            cart={cart}
            onAddItem={onAddItem}
            onRemoveItem={onRemoveItem}
            imageViewType={imageViewType}
          />
        ) : (
          <EmptySearchResult
            selectedCategoryId={selectedCategoryId}
            searchStr={searchStr}
          />
        )}
        <div className="mt-40 mb-40 w-20">
          <NetworkAsset assetName="footer" />
        </div>
      </div>
    </div>
  );
};

export default CategoryItemList;

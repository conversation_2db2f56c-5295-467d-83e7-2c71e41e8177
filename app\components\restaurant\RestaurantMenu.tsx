import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  AvailableItem,
  Cart,
  ItemCategoryDtos,
  ItemOptionsData,
  ImageViewType
} from "~/types";
import { CategoryListL2 } from "../chooseitem/CategoryListL2";
import { chooseitemsStore } from "~/stores/chooseitems.store";
import { NetworkAsset } from "../NetworkAssests";
import MenuFloatingButton from "./MenuFloatingButton";
import { EmptySearchResult } from "../chooseitem/EmptySearchResult";
import { ChevronDown, ChevronUp } from "lucide-react";
import { ItemList } from "../chooseitem/ItemList";
import RestaurantItemSearch from "./RestaurantItemSearch";
import { Button } from "node_modules/@headlessui/react/dist/components/button/button";
import Banners from "../Banners";
import { cn } from "~/utils/cn";
import { getSortedMenuData } from "~/utils/menuUtils";

interface CategoryItemListProps {
  data: ItemOptionsData;
  cart: Cart;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  searchStr: string;
  handleSelectL1: (categoryId: number) => void;
  imageViewType: ImageViewType;
  menuButtonPosition?: string;
}

const CategoryItemList: React.FC<CategoryItemListProps> = ({
  data,
  cart,
  onAddItem,
  onRemoveItem,
  searchStr,
  handleSelectL1,
  imageViewType,
  menuButtonPosition
}) => {
  const {
    categoryType,
    setCategoryType,
    loading,
    setSelectedCategory,
    setSelectedParentCategory,
    setCategoryTypeList
  } = chooseitemsStore((state) => state);

  const [selectedCategoryId, setSelectedCategoryId] = useState<number>(
    data?.itemCategoryDtos?.[0].id
  ); // Stores the selected category ID
  const [filteredItems, setFilteredItems] = useState<AvailableItem[]>(
    data?.availableItems || []
  );

  // New state for locally filtered items (by search and tags)
  const [locallyFilteredItems, setLocallyFilteredItems] = useState<
    AvailableItem[]
  >(data?.availableItems || []);

  // State to track if we're currently searching
  const [isSearchActive, setIsSearchActive] = useState(false);

  // Create ref map for category sections
  const categoryRefs = useRef<{ [key: number]: HTMLDivElement | null }>({});

  // Filter function to check if item should be displayed
  const shouldDisplayItem = (item: AvailableItem | undefined): boolean => {
    if (!item) return false;
    const hasPrice = item.pricePerUnit > 0;
    const hasAogItems =
      (item.pricePerUnit === 0 && item.aogList && item.aogList.length > 0) ||
      false;
    const hasVariants =
      (item.pricePerUnit === 0 &&
        item.itemVariationList &&
        item.itemVariationList.length > 0) ||
      false;

    return hasPrice || hasAogItems || hasVariants;
  };

  const scrollToCategory = (categoryId: number) => {
    if (categoryRefs.current[categoryId]) {
      categoryRefs.current[categoryId]?.scrollIntoView({
        behavior: "smooth",
        block: "start"
      });
    }
  };

  useEffect(() => {
    if (data?.itemCategoryL3Dtos?.length && data?.itemCategoryL2Dtos?.length) {
      setCategoryType("L3");
      setCategoryTypeList("L3");
    } else if (data?.itemCategoryL2Dtos?.length) {
      setCategoryType("L2");
      setCategoryTypeList("L2");
    } else if (data?.itemCategoryDtos?.length) {
      setCategoryType("L1");
      setCategoryTypeList("L1");

      // Don't filter by category, show all items
      setFilteredItems([...(data?.availableItems || [])]);
      setLocallyFilteredItems([...(data?.availableItems || [])]);
      setSelectedCategoryId(
        data.selectedL1CategoryId || data.itemCategoryDtos?.[0].id
      );
    } else {
      setCategoryType("L0");
      setCategoryTypeList("L0");
      setFilteredItems([...(data?.availableItems || [])]);
      setLocallyFilteredItems([...(data?.availableItems || [])]);
    }
  }, [
    data?.itemCategoryL3Dtos,
    data?.itemCategoryL2Dtos,
    data?.itemCategoryDtos,
    data?.availableItems,
    setCategoryTypeList,
    setCategoryType,
    data.selectedL1CategoryId,
    loading
  ]);

  // filter items by category Id
  useEffect(() => {
    if (searchStr?.length > 3) {
      // Only filter by search text, not by category
      const filteredItems =
        data?.availableItems?.filter(
          (item) =>
            shouldDisplayItem(item) &&
            item?.itemName
              .toLocaleLowerCase()
              .includes(searchStr.toLocaleLowerCase())
        ) || [];

      setFilteredItems([...filteredItems] as AvailableItem[]);
      setLocallyFilteredItems([...filteredItems] as AvailableItem[]);
    } else {
      // If no search term, use all available items
      setFilteredItems([
        ...(data?.availableItems?.filter(shouldDisplayItem) || [])
      ] as AvailableItem[]);
      setLocallyFilteredItems([
        ...(data?.availableItems?.filter(shouldDisplayItem) || [])
      ] as AvailableItem[]);
    }
  }, [searchStr, data.availableItems]);

  // Handler for selecting a category
  const handleSelectCategory = (categoryId: number) => {
    if (categoryType !== "L1") {
      handleSelectL1(categoryId);

      setSelectedParentCategory(
        data?.itemCategoryL2Dtos?.find((cat) => cat.id === categoryId)
      );
    } else {
      // Set the selected category ID for UI highlighting purposes
      // But don't filter the items by category
      // if (!searchStr?.length) {
      //   handleSelectL1(categoryId);
      // }
      setSelectedCategoryId(categoryId);
      setSelectedCategory(categoryId);

      // Ensure we're still showing all items
      // setFilteredItems([...(data?.availableItems || [])] as AvailableItem[]);
      // setLocallyFilteredItems([
      //   ...(data?.availableItems || [])
      // ] as AvailableItem[]);
    }
  };

  // Handler for when the search component filters items - use useCallback to memoize
  const handleFilteredItemsChange = useCallback(
    (items: AvailableItem[], searchActive: boolean) => {
      setLocallyFilteredItems(items);
      setIsSearchActive(searchActive);
    },
    []
  );

  return (
    <>
      {/* <div className="no-scrollbar"> */}
      {data.itemCategoryL3Dtos || data.itemCategoryL2Dtos ? (
        <div className="flex bg-[#fcfcfd] pt-2 h-full w-full pb-16 no-scrollbar">
          <CategoryListL2
            categoriesL3={data.itemCategoryL3Dtos}
            categoriesL2={data.itemCategoryL2Dtos}
            selectedCategoryId={selectedCategoryId}
            onSelectCategory={handleSelectCategory}
          />

          {/* Add floating menu button for L2/L3 categories */}
          <MenuFloatingButton
            categories={data.itemCategoryL2Dtos || []}
            onSelectCategory={handleSelectCategory}
            scrollToCategory={scrollToCategory}
          />
        </div>
      ) : (
        <RestaurantItemLayout
          data={data}
          cart={cart}
          selectedCategoryId={selectedCategoryId || data.selectedL1CategoryId}
          searchStr={searchStr}
          categories={data?.itemCategoryDtos || []}
          filteredItems={filteredItems}
          locallyFilteredItems={locallyFilteredItems}
          isSearchActive={isSearchActive}
          handleSelectCategory={handleSelectCategory}
          onAddItem={onAddItem}
          onRemoveItem={onRemoveItem}
          categoryRefs={categoryRefs}
          scrollToCategory={scrollToCategory}
          imageViewType={imageViewType}
          onFilteredItemsChange={handleFilteredItemsChange}
          menuButtonPosition={menuButtonPosition}
        />
      )}
      {/* </div> */}
    </>
  );
};

// Type for the item data grouped by category
interface ItemsByCategoryType {
  [key: number]: AvailableItem[];
}

// Generic header component to be reused across different sections
interface SectionHeaderProps {
  title: string;
  itemCount: number;
  rightContent?: React.ReactNode;
  onClick?: (e: React.MouseEvent) => void;
}

const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  rightContent,
  onClick
}) => (
  <Button
    className="bg-white py-2.5 px-2 w-full flex justify-between items-center "
    onClick={onClick}
  >
    <h2 className="text-lg font-semibold tracking-wide text-gray-700">
      {title}
      {/* <span className="ml-2 text-sm text-gray-500">({itemCount})</span> */}
    </h2>
    {rightContent}
  </Button>
);

// Content wrapper for consistent content styling
interface ContentWrapperProps {
  children: React.ReactNode;
  id?: string;
}

const ContentWrapper: React.FC<ContentWrapperProps> = ({ children, id }) => (
  <div id={id} className="pb-2 bg-white no-scrollbar">
    {children}
  </div>
);

// Generic toggle button component for expanding/collapsing categories
interface ToggleButtonProps {
  isExpanded: boolean;
  onClick: (e: React.MouseEvent) => void;
  ariaLabel?: string;
}

const ToggleButton: React.FC<ToggleButtonProps> = ({
  isExpanded,
  onClick,
  ariaLabel
}) => (
  <button
    className="p-1 rounded-full hover:bg-gray-100 focus:outline-none"
    onClick={onClick}
    aria-label={ariaLabel || (isExpanded ? "Collapse" : "Expand")}
  >
    {isExpanded ? (
      <ChevronUp className="h-6 w-6 text-gray-800" />
    ) : (
      <ChevronDown className="h-6 w-6 text-gray-800" />
    )}
  </button>
);

// Interface for search results list props
interface SearchResultsListProps {
  items: AvailableItem[];
  cart: Cart;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  approxPricing?: boolean;
  imageViewType: ImageViewType;
  stickyOffset?: number; // Add offset to account for the search bar height
}

// Interface for category section props
interface CategorySectionProps {
  categoryId: number;
  category: ItemCategoryDtos;
  items: AvailableItem[];
  cart: Cart;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  approxPricing?: boolean;
  imageViewType: ImageViewType;
  isExpanded: boolean;
  onToggle: (categoryId: number, e: React.MouseEvent) => void;
  innerRef: React.Ref<HTMLDivElement>;
  stickyOffset?: number; // Add offset to account for the search bar height
}

// Interface for flat item list props
interface FlatItemListProps {
  items: AvailableItem[];
  cart: Cart;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  approxPricing?: boolean;
  imageViewType: ImageViewType;
  stickyOffset?: number; // Add offset to account for the search bar height
}

// Refactored search results list using the new components
const SearchResultsList: React.FC<SearchResultsListProps> = ({
  items,
  cart,
  onAddItem,
  onRemoveItem,
  approxPricing,
  imageViewType,
  stickyOffset = 56
}) => (
  <div className="w-full mt-2">
    <div
      className="sticky w-full"
      style={{ top: `${stickyOffset}px`, zIndex: 20 }}
    >
      <div className="overflow-hidden bg-white mx-2 rounded-t-lg">
        <SectionHeader title="Search Results" itemCount={items.length} />
      </div>
    </div>
    <div className="mx-2 overflow-hidden bg-white border-gray-200 rounded-b-lg shadow-lg">
      <ContentWrapper>
        <ItemList
          items={items}
          cart={cart}
          onAddItem={onAddItem}
          onRemoveItem={onRemoveItem}
          approxPricing={approxPricing ?? false}
          imageViewType={imageViewType}
        />
      </ContentWrapper>
    </div>
  </div>
);

// Component for category header with toggle functionality
interface CategoryToggleHeaderProps {
  category: ItemCategoryDtos;
  itemCount: number;
  isExpanded: boolean;
  onToggle: (categoryId: number, e: React.MouseEvent) => void;
}

const CategoryToggleHeader: React.FC<CategoryToggleHeaderProps> = ({
  category,
  itemCount,
  isExpanded,
  onToggle
}) => {
  const handleToggle = (e: React.MouseEvent) => onToggle(category.id, e);

  return (
    <SectionHeader
      title={category.name}
      itemCount={itemCount}
      onClick={handleToggle}
      rightContent={
        <ToggleButton
          isExpanded={isExpanded}
          onClick={handleToggle}
          ariaLabel={isExpanded ? "Collapse category" : "Expand category"}
        />
      }
    />
  );
};

// Refactored category section using the new components
const CategorySection: React.FC<CategorySectionProps> = ({
  categoryId,
  category,
  items,
  cart,
  onAddItem,
  onRemoveItem,
  approxPricing,
  imageViewType,
  isExpanded,
  onToggle,
  innerRef,
  stickyOffset = 56
}) => {
  const toggleHeader = (
    <CategoryToggleHeader
      category={category}
      itemCount={items.length}
      isExpanded={isExpanded}
      onToggle={onToggle}
    />
  );

  return (
    <div ref={innerRef} className="w-full px-2 py-2">
      <div className="rounded-lg bg-white shadow-[0px_2px_4px_0px_rgba(0,0,0,0.25)]">
        <div
          className="sticky w-full"
          style={{ top: `${stickyOffset}px`, zIndex: 20 }}
        >
          <div
            className={cn(
              "overflow-hidden bg-white",
              !isExpanded ? "rounded-lg" : "rounded-lg"
            )}
          >
            {toggleHeader}
          </div>
        </div>
        {isExpanded && (
          <div className="overflow-hidden border-gray-200 rounded-b-lg shadow-lg">
            <ContentWrapper id={`category-content-${categoryId}`}>
              <ItemList
                items={items}
                cart={cart}
                onAddItem={onAddItem}
                onRemoveItem={onRemoveItem}
                approxPricing={approxPricing ?? false}
                imageViewType={imageViewType}
              />
            </ContentWrapper>
          </div>
        )}
      </div>
    </div>
  );
};

// Refactored flat item list using the new components
const FlatItemList: React.FC<FlatItemListProps> = ({
  items,
  cart,
  onAddItem,
  onRemoveItem,
  approxPricing,
  imageViewType,
  stickyOffset = 56
}) => (
  <div className="w-full px-3 py-2">
    <div className="rounded-lg bg-white shadow-[0px_2px_4px_0px_rgba(0,0,0,0.25)]">
      <div
        className="sticky w-full"
        style={{ top: `${stickyOffset}px`, zIndex: 20 }}
      >
        <div className={cn("overflow-hidden bg-white", "rounded-lg")}>
          <SectionHeader title="All Items" itemCount={items.length} />
        </div>
      </div>
      {
        <div className="overflow-hidden border-gray-200 rounded-b-lg shadow-lg">
          <ContentWrapper>
            <ItemList
              items={items}
              cart={cart}
              onAddItem={onAddItem}
              onRemoveItem={onRemoveItem}
              approxPricing={approxPricing ?? false}
              imageViewType={imageViewType}
            />
          </ContentWrapper>
        </div>
      }
    </div>
  </div>
);

// Pure component for the sticky search bar
interface StickySearchBarProps {
  children: React.ReactNode;
}

const StickySearchBar: React.FC<StickySearchBarProps> = ({ children }) => (
  <div className="sticky shadow-md" style={{ top: 0, zIndex: 50 }}>
    {children}
  </div>
);

// Pure component for footer
const Footer: React.FC = () => (
  <div className="mt-40 mb-40 w-20">
    <NetworkAsset assetName="footer" />
  </div>
);

interface RestaurantItemLayoutProps {
  data: ItemOptionsData;
  cart: Cart;
  selectedCategoryId?: number;
  searchStr: string;
  categories: ItemCategoryDtos[];
  filteredItems: AvailableItem[];
  locallyFilteredItems: AvailableItem[];
  isSearchActive: boolean;
  menuButtonPosition?: string;
  handleSelectCategory: (categoryId: number) => void;
  onAddItem: (item: AvailableItem) => void;
  onRemoveItem: (item: AvailableItem) => void;
  categoryRefs?: React.MutableRefObject<{
    [key: number]: HTMLDivElement | null;
  }>;
  scrollToCategory?: (categoryId: number) => void;
  imageViewType: ImageViewType;
  onFilteredItemsChange: (
    items: AvailableItem[],
    isSearchActive: boolean
  ) => void;
}

const RestaurantItemLayout: React.FC<RestaurantItemLayoutProps> = ({
  data,
  cart,
  selectedCategoryId,
  searchStr,
  categories,
  filteredItems,
  locallyFilteredItems,
  isSearchActive,
  handleSelectCategory,
  onAddItem,
  onRemoveItem,
  categoryRefs,
  scrollToCategory,
  imageViewType,
  onFilteredItemsChange,
  menuButtonPosition
}) => {
  // State to track expanded categories - default all expanded (true)
  const [expandedCategories, setExpandedCategories] = useState<
    Record<number, boolean>
  >({});

  // Remove the isScrolling state
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // Initialize all categories as expanded by default
  useEffect(() => {
    const initialExpandedState: Record<number, boolean> = {};
    categories.forEach((cat) => {
      initialExpandedState[cat.id] = true;
    });
    setExpandedCategories(initialExpandedState);
  }, [categories]);

  // Remove the scroll event handling useEffect

  // Toggle expand/collapse for a category
  const toggleCategoryExpand = (categoryId: number, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering other handlers
    setExpandedCategories((prev) => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  // Enhanced handleCategorySelect that also ensures expansion
  const handleCategorySelect = (categoryId: number) => {
    // Ensure the category is expanded when selected
    setExpandedCategories((prev) => ({
      ...prev,
      [categoryId]: true
    }));

    // Call the original handler passed from parent
    handleSelectCategory(categoryId);
  };

  // Group items by category for rendering with ref anchors
  const itemsByCategory = React.useMemo(() => {
    const grouped: ItemsByCategoryType = {};

    if (categories.length === 0 || locallyFilteredItems.length === 0) {
      return grouped;
    }

    // Initialize categories
    categories.forEach((category) => {
      grouped[category.id] = [];
    });

    // Group items by their categories - ensure items can belong to multiple categories
    locallyFilteredItems.forEach((item) => {
      if (item.itemCategories && item.itemCategories.length > 0) {
        // For each category the item belongs to, add it to that category's group
        item.itemCategories.forEach((categoryId) => {
          if (grouped[categoryId]) {
            grouped[categoryId].push(item);
          }
        });
      }
    });

    return grouped;
  }, [categories, locallyFilteredItems]);

  // Apply sorting to categories and items
  const { sortedCategories, sortedItemsByCategory } = React.useMemo(() => {
    // When searching, don't apply sorting
    if (isSearchActive) {
      return {
        sortedCategories: categories,
        sortedItemsByCategory: itemsByCategory
      };
    }

    return getSortedMenuData(categories, itemsByCategory);
  }, [categories, itemsByCategory, isSearchActive]);

  const hasCategorizedItems = Object.values(sortedItemsByCategory).some(
    (items) => items.length > 0
  );

  useEffect(() => {
    scrollContainerRef.current?.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  }, [locallyFilteredItems]);

  return (
    <div className={`flex flex-col h-full w-full no-scrollbar`}>
      <StickySearchBar>
        <RestaurantItemSearch
          items={filteredItems}
          onFilteredItemsChange={onFilteredItemsChange}
          className="w-full bg-white"
        />
      </StickySearchBar>

      <div
        ref={scrollContainerRef}
        className={`flex flex-col items-center overflow-y-auto overflow-x-hidden w-full`}
      >
        {/* Banner - Now scrolls with content */}
        {data?.sellerBanners?.length > 0 && (
          <div className="w-full p-3">
            <Banners
              images={data.sellerBanners}
              className="w-full h-fit"
              imageClassName="aspect-[2/1] object-cover"
              swiperConfig={{
                slidesPerView: 1.12,
                spaceBetween: 8,
                loop: true
              }}
              autoplay={true}
              pagination={false}
            />
          </div>
        )}

        {locallyFilteredItems.length > 0 ? (
          <>
            {/* When search is active, show search results */}
            {isSearchActive ? (
              <SearchResultsList
                items={locallyFilteredItems}
                cart={cart}
                onAddItem={onAddItem}
                onRemoveItem={onRemoveItem}
                approxPricing={data.approxPricing || false}
                imageViewType={imageViewType}
                stickyOffset={0}
              />
            ) : hasCategorizedItems ? (
              // Iterate through categories in their sorted order
              sortedCategories.map((category) => {
                const items = sortedItemsByCategory[category.id] || [];

                // Skip empty categories
                if (items.length === 0) return null;

                const isExpanded = expandedCategories[category.id];

                return (
                  <CategorySection
                    key={category.id}
                    categoryId={category.id}
                    category={category}
                    items={items}
                    cart={cart}
                    onAddItem={onAddItem}
                    onRemoveItem={onRemoveItem}
                    approxPricing={data.approxPricing || false}
                    imageViewType={imageViewType}
                    isExpanded={isExpanded}
                    onToggle={toggleCategoryExpand}
                    innerRef={(el: HTMLDivElement | null) =>
                      categoryRefs && (categoryRefs.current[category.id] = el)
                    }
                    stickyOffset={0}
                  />
                );
              })
            ) : (
              // If no categories, show a flat list
              <FlatItemList
                items={locallyFilteredItems}
                cart={cart}
                onAddItem={onAddItem}
                onRemoveItem={onRemoveItem}
                approxPricing={data.approxPricing || false}
                imageViewType={imageViewType}
                stickyOffset={0}
              />
            )}
          </>
        ) : (
          <EmptySearchResult
            selectedCategoryId={selectedCategoryId}
            searchStr={searchStr}
          />
        )}
        <Footer />
      </div>

      {/* Add floating menu button only when not searching */}
      {categories.length > 0 && scrollToCategory && !isSearchActive && (
        <div className="transition-all duration-200 ">
          <MenuFloatingButton
            categories={sortedCategories}
            onSelectCategory={handleCategorySelect}
            scrollToCategory={scrollToCategory}
            itemsByCategory={sortedItemsByCategory}
            menuButtonPosition={menuButtonPosition}
          />
        </div>
      )}
    </div>
  );
};

export default CategoryItemList;

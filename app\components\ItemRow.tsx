// app/components/ItemRow.tsx

import React from "react";
import { SellerOrderItem } from "~/types";
import CustomImage from "./CustomImage";
import AddItemButtonV2 from "./chooseitem/AddItemButtonV2";
import { roundOff } from "@utils/roundOff";
import SupplierBadge from "./common/SupplierBadge";
interface ItemRowProps {
  itemDetails: SellerOrderItem;
  onAdd?: () => void;
  onRemove?: () => void;
  quantity?: number;
  itemType?: "confirmed" | "new";
  amount?: number;
  displayPrices?: boolean;
}

const ItemRow: React.FC<ItemRowProps> = ({
  itemDetails,
  onAdd,
  onRemove,
  quantity = 0,
  amount = 0,
  itemType = "new",
  displayPrices
}) => {
  const avgPrice =
    roundOff(
      itemType === "confirmed"
        ? itemDetails.availableCartItem.orderedAmount /
            itemDetails.availableCartItem.orderedQty
        : itemDetails.pricePerUnit
    ) || 0;
  return (
    <div>
      {(!itemDetails.isSoldOut || itemDetails.quantity > 0) &&
        !itemDetails.availableCartItem.freeItem && (
          <div className="flex justify-between items-center gap-3">
            <div className="flex items-stretch gap-2">
              <CustomImage
                src={itemDetails.itemUrl}
                alt={""}
                className={`${
                  itemType === "confirmed" ? "w-10 h-10" : "w-16 h-16"
                } bg-neutral-50 rounded-lg aspect-square border border-neutral-100 object-fill`}
              />
              <div className="flex flex-col justify-between">
                <div className="flex flex-col gap-0">
                  <h3
                    className={`text-sm font-medium overflow-hidden text-typography-700 line-clamp-1`}
                  >
                    {itemDetails.itemName}
                  </h3>
                  {itemDetails.packaging && itemType !== "confirmed" && (
                    <div className="text-xs font-normal text-typography-200">
                      {itemDetails.packaging}
                    </div>
                  )}
                </div>
                {itemDetails.pricePerUnit && (
                  <div className="text-xs align-bottom font-light text-typography-400">
                    {itemType === "confirmed" ? (
                      <>
                        <span className="text-sm font-semibold text-primary">
                          {`${quantity} ${
                            itemDetails.unit === "unit" ? "" : itemDetails.unit
                          }`}
                          &nbsp;
                        </span>
                      </>
                    ) : null}
                    {itemType === "confirmed" && displayPrices !== false && (<span className="text-sm font-medium text-typography-300">x &nbsp;</span>)}
                    {displayPrices !== false  && <span className="text-sm font-medium text-typography-300">
                      {`₹ ${avgPrice} ${
                        itemDetails.unit === "unit"
                          ? ""
                          : " / " + itemDetails.unit
                      }`}{" "}
                    </span>}
                  </div>
                )}
                {itemDetails.availableCartItem.supplier !== "" ? (
                  <div>
                    <SupplierBadge
                      supplier={itemDetails.availableCartItem.supplier ?? ""}
                    />
                  </div>
                ) : null}
              </div>
            </div>
            {
              <div className="flex flex-col items-end gap-2">
                {/* <p className="text-sm font-thin text-typography-800 pr-2"> */}
                { displayPrices !== false && <p
                  className={`text-sm font-medium overflow-hidden text-typography-700 ${
                    itemType === "confirmed"
                      ? "text-sm font-medium text-typography-600"
                      : "text-xs font-thin text-typography-300"
                  }`}
                >
                  {`₹${roundOff(amount)}`}
                </p>}
                {itemType === "new" &&
                  onAdd &&
                  onRemove &&
                  !itemDetails.availableCartItem.freeItem && (
                    <AddItemButtonV2
                      qty={quantity}
                      onAdd={onAdd}
                      onRemove={onRemove}
                      isDisabled={itemDetails.isSoldOut}
                      unit={itemDetails.unit}
                      btnConfig={{
                        showUnit: false
                      }}
                    />
                  )}
              </div>
            }
          </div>
        )}
      {itemDetails.isSoldOut &&
        itemDetails.quantity === 0 &&
        !itemDetails.availableCartItem.freeItem && (
          <div className="relative flex justify-between items-center py-1 backdrop-blur-md">
            <div className="flex items-stretch gap-2">
              <CustomImage
                src={itemDetails.itemUrl}
                alt={itemDetails.itemName}
                className="w-10 h-10 bg-neutral-50 rounded-lg aspect-square border border-neutral-100 object-fill"
              />
              <div className="flex flex-col justify-between gap-1">
                <div className="flex flex-col gap-0">
                  <h3 className="text-sm font-medium w-full overflow-hidden text-typography-700 line-clamp-1">
                    {itemDetails.itemName}
                  </h3>
                  {itemDetails.packaging && (
                    <div className="text-xs font-normal text-typography-200">
                      {itemDetails.packaging}
                    </div>
                  )}
                </div>
                {itemDetails.quantity > 0 && (
                  <p className="text-xs font-light text-typography-300">
                    {`${itemDetails.quantity} ${itemDetails.unit} `}
                  </p>
                )}
                {itemDetails.availableCartItem.supplier !== "" ? (
                  <div>
                    <SupplierBadge
                      supplier={itemDetails.availableCartItem.supplier ?? ""}
                    />
                  </div>
                ) : null}
              </div>
            </div>
          </div>
        )}
      {itemDetails.availableCartItem.freeItem && newFunction()}
    </div>
  );

  function newFunction(): React.ReactNode {
    return (
      <div className="relative flex justify-between items-center py-3 backdrop-blur-md bg-[linear-gradient(88deg,#DFF9F8_0%,#FFF_100%)] rounded-md px-2 my-2 ">
        <div className="flex items-stretch gap-2">
          <CustomImage
            src={itemDetails.itemUrl}
            alt={itemDetails.itemName}
            className="w-12 h-12 bg-neutral-50 rounded-lg aspect-square border border-neutral-100 object-fill"
          />
          <div className="flex flex-col justify-between gap-1">
            <div className="flex flex-col gap-0">
              <h3 className="text-sm font-medium w-full overflow-hidden text-typography-700 line-clamp-1">
                {itemDetails.itemName}
              </h3>
              {itemDetails.packaging && (
                <div className="text-xs font-normal text-typography-200">
                  {itemDetails.packaging}
                </div>
              )}
            </div>
            {itemDetails.quantity > 0 && (
              <p className="text-xs font-light text-typography-300">
                {`${itemDetails.quantity} ${itemDetails.unit} `}
              </p>
            )}
            {itemDetails.availableCartItem.supplier !== "" ? (
              <div>
                <SupplierBadge
                  supplier={itemDetails.availableCartItem.supplier ?? ""}
                />
              </div>
            ) : null}
          </div>
        </div>
        <span className="text-[#00B6BE] font-semibold">Free</span>
      </div>
    );
  }
};

export default ItemRow;

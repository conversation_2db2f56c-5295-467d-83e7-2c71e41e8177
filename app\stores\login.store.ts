import { create } from "zustand";

interface LoginState {
  // State
  isLoginOpen: boolean;
  redirectPath: string | null;

  // Actions
  openLogin: () => void;
  closeLogin: () => void;
  setRedirectPath: (path: string | null) => void;
}

export const useLoginStore = create<LoginState>((set) => ({
  // Initial state
  isLoginOpen: false,
  redirectPath: null,

  // Actions
  openLogin: () => set({ isLoginOpen: true }),
  closeLogin: () => set({ isLoginOpen: false }),
  setRedirectPath: (path) => set({ redirectPath: path })
}));

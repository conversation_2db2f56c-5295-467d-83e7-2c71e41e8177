// Facebook Conversion API Events API Route
// Handles client-side event submissions and forwards them to Facebook

import { json, type ActionFunction } from "@remix-run/node";
import {
  FacebookConversionEvent,
  FacebookEventName,
} from "~/types/capi-fb";
import {
  createFacebookConversionApiClient,
  sendEventsWithRetry,
} from "~/services/capi-server";
import {
  FacebookEventBuilder,
  createViewContentEvent,
  createAddToCartEvent,
  createInitiateCheckoutEvent,
  createPurchaseEvent,
  generateEventId,
} from "~/services/capi-builder";
import { CLIDData, retrieveCLIDData } from "~/utils/capi-clid";

/**
 * Request payload for Facebook Conversion API events
 */
interface FacebookEventsRequest {
  events: Array<{
    eventName: FacebookEventName;
    eventData?: any;
    eventId?: string;
    timestamp?: number;
  }>;
}

/**
 * Response for Facebook Conversion API events
 */
interface FacebookEventsResponse {
  success: boolean;
  message: string;
  eventsProcessed?: number;
  errors?: string[];
}

/**
 * Handle POST requests to send events to Facebook Conversion API
 */
export const action: ActionFunction = async ({ request }) => {
  if (request.method !== "POST") {
    return json<FacebookEventsResponse>(
      { success: false, message: "Method not allowed" },
      { status: 405 }
    );
  }

  try {
    // Parse request body
    const body: FacebookEventsRequest = await request.json();
    const { events } = body;

    if (!events || events.length === 0) {
      return json<FacebookEventsResponse>(
        { success: false, message: "No events provided" },
        { status: 400 }
      );
    }

    // Retrieve clid Data
    const clidData = retrieveCLIDData(request);

    // Check if Facebook Conversion API is enabled
    if (!clidData || !clidData.ctwa_token || !clidData.wabDatasetId) {
      return json<FacebookEventsResponse>({
        success: false,
        message: "Facebook Conversion API is disabled",
        eventsProcessed: 0,
      }, { status: 400 });
    }

    // Create Facebook Conversion API client
    const facebookClient = createFacebookConversionApiClient(clidData);
    if (!facebookClient) {
      return json<FacebookEventsResponse>(
        { success: false, message: "Facebook Conversion API client not configured" },
        { status: 500 }
      );
    }

    // Convert events to Facebook Conversion API format
    const facebookEvents: FacebookConversionEvent[] = [];
    const errors: string[] = [];

    for (const event of events) {
      try {
        const facebookEvent = await convertToFacebookEvent(event, clidData);
        if (facebookEvent) {
          facebookEvents.push(facebookEvent);
        }
      } catch (error) {
        console.error(`Error converting event ${event.eventName}:`, error.message);
        errors.push(`Failed to convert event ${event.eventName}: ${error.message}`);
      }
    }

    if (facebookEvents.length === 0) {
      return json<FacebookEventsResponse>(
        { 
          success: false, 
          message: "No valid events to send",
          errors 
        },
        { status: 400 }
      );
    }

    // Send events to Facebook
    try {
      const response = await sendEventsWithRetry(facebookClient, facebookEvents);
      
      return json<FacebookEventsResponse>({
        success: true,
        message: "Events sent successfully",
        eventsProcessed: facebookEvents.length,
        errors: errors.length > 0 ? errors : undefined,
      });
    } catch (error) {
      console.error("Error sending events to Facebook:", error.message);
      return json<FacebookEventsResponse>(
        { 
          success: false, 
          message: "Failed to send events to Facebook",
          errors: [error.message, ...errors]
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error processing Facebook Conversion API request:",error.message);
    return json<FacebookEventsResponse>(
      { success: false, message: "Internal server error" },
      { status: 500 }
    );
  }
};

/**
 * Convert client-side event to Facebook Conversion API event
 */
async function convertToFacebookEvent(
  event: {
    eventName: FacebookEventName;
    eventData?: any;
    eventId?: string;
    timestamp?: number;
  },
  clidData: CLIDData,
): Promise<FacebookConversionEvent | null> {
  const { eventName, timestamp, eventId, eventData = {} } = event;
  const { ctwa_clid, sourceUrl } = clidData;


  const commonData = {
    clid: ctwa_clid || "",
    eventId: eventId || generateEventId(eventName.substring(0, 3).toUpperCase()),
    timestamp,
  };

  switch (eventName) {
    case "ViewContent":
      return createViewContentEvent({
        ...eventData,
        ...commonData,
      });

    case "AddToCart":
      return createAddToCartEvent({
        ...eventData,
        ...commonData,
      });

    case "InitiateCheckout":
      return createInitiateCheckoutEvent({
        ...eventData,
        ...commonData,
      });

    case "Purchase":
      return createPurchaseEvent({
        ...eventData,
        ...commonData,
      });

    default:
      // For other events, use the generic builder
      const builder = new FacebookEventBuilder()
        .setEventName(eventName)
        .setCLID(ctwa_clid)
        .setEventSourceUrl(sourceUrl || "");

      if (eventId) {
        builder.setEventId(eventId);
      }

      if (timestamp) {
        builder.setEventTime(timestamp);
      }

      if (eventData?.phoneNumber) {
        builder.setPhoneNumber(eventData.phoneNumber);
      }

      if (eventData?.externalId) {
        builder.setExternalId(eventData.externalId);
      }

      // Add custom data if provided
      if (eventData && Object.keys(eventData).length > 0) {
        builder.setCustomData(eventData);
      }

      return builder.build();
  }
}

/**
 * Handle GET requests for health check
 */
export const loader = async () => {
  return json({
    service: "Facebook Conversion API Events",
    status: "healthy",
    timestamp: new Date().toISOString(),
  });
};

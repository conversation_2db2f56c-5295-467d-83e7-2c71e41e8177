import dayjs from "dayjs";

export const formatCurrency = (amount: number, decimals = 2): string => {
  return new Intl.NumberFormat("en-IN", {
    style: "currency",
    currency: "INR",
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(amount);
};

export const getEstDeliveryTime = (estDeliveryTime: string) => {
  if (!estDeliveryTime) return "0";
  const digit = estDeliveryTime.match(/\d+/);
  return digit ? digit[0] : "0";
};

export const IsValidTime = (time: string): boolean => {
  const validFormats = ["HH", "HH:mm", "HH:mm:ss"];
  return validFormats.some((format) => dayjs(time, format, true).isValid());
};

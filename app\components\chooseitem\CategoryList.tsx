import React from "react";
import { ItemCategoryDtos } from "~/types";
import CategoryItem from "./CategoryItem";
import CategoryItemV2 from "./CategoryItemV2";

interface CategoryListProps {
  categories: ItemCategoryDtos[];
  selectedCategoryId: number | undefined;
  onSelectCategory: (categoryId: number) => void;
  allowScrolling?: boolean;
}
export const CategoryList: React.FC<CategoryListProps> = ({
  categories,
  selectedCategoryId,
  onSelectCategory,
  allowScrolling = true,
}) => {
  return (
    <div className={`bg-white rounded-t-lg rounded-br-lg ${allowScrolling?"overflow-y-scroll":"overflow-y-hidden"} w-24 pb-16 no-scrollbar`}>
      {categories?.map((category) => (
        <CategoryItem
          key={category.id}
          category={category}
          isSelected={selectedCategoryId === category.id}
          onSelect={() => onSelectCategory(category.id)}
        />
      ))}
    </div>
  );
};

import { json } from "@remix-run/node";
import { useNavigate, useLocation } from "@remix-run/react";
import { Order } from "~/types";
import OrderTimeline from "~/components/orders/OrderTimeline";
import OrderItemList from "~/components/orders/OrderItemList";
import OrderBillSummary from "~/components/orders/OrderBillSummary";
import OrderAdditionalDetails from "~/components/orders/OrderAdditionalDetails";
import { ArrowLeftIcon } from "lucide-react";
import PayNow from "~/components/PayNow";
import { formatCurrency } from "~/utils/format";
import { orderToPaymentRequest } from "~/types/payment";
import { useState } from "react";
// Loader function to handle unauthorized access
export async function loader() {
  return json({});
}

export default function OrderDetails() {
  const navigate = useNavigate();
  const location = useLocation();
  const [order, setOrder] = useState<Order | undefined>(
    location.state?.order as Order | undefined
  );

  const handleBack = () => {
    navigate(-1);
  };

  if (!order) {
    return (
      <div className="flex items-center justify-center h-screen">
        <p className="text-gray-500">No order details available</p>
      </div>
    );
  }

  const showPayNow = order.balanceTobePaid > 0 && order.displayPrices !== false;

  return (
    <div className="flex flex-col bg-gray-100 min-h-screen pb-20">
      {/* Header with back button and title */}
      <div className="fixed top-0 z-10 bg-white px-4 py-3 flex items-center border-b w-full">
        <button className="mr-4" onClick={handleBack}>
          <ArrowLeftIcon className="w-5 h-5 text-gray-500" />
        </button>
        <h1 className="text-lg font-semibold">Order Details</h1>
      </div>

      <div className="flex-1 p-4 space-y-4 mb-20 mt-12">
        <OrderTimeline orderDetails={order} />
        <OrderItemList order={order} displayPrices={order.displayPrices}/>
        { order.displayPrices !== false && <OrderBillSummary order={order} />}
        <OrderAdditionalDetails order={order} />
      </div>

      {showPayNow && (
        <div className="fixed bottom-2 z-10 px-2 w-full">
          <PayNow
            paymentRequest={orderToPaymentRequest(order)}
            buttonClassName="z-10 bottom-0 p-3 rounded-xl flex justify-center align-center gap-1 w-full bg-primary hover:bg-primary-600 text-white shadow-sm"
            buttonText={`Pay ${formatCurrency(order.balanceTobePaid)}`}
            onSuccess={() => navigate(0)}
          />
        </div>
      )}
    </div>
  );
}

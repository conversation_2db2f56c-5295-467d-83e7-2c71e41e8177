// app/types/index.ts
import { AddressDto } from "./address.types";
import { CouponDTO, AppliedCouponDTO } from "./coupon.types";
export interface User {
  userId: number;
  userName: string;
  businessName: string;
  buyerId: number;
  buyerFromBC?: boolean;
  mobileNumber?: string;
}

export interface AogList {
  id: number;
  minSelect: number;
  maxSelect: number;
  name: string;
  description: string;
  seq: number;
  addOnItemList: AddonItem[];
}

export interface AddonItem {
  id: string;
  sId: string;
  name: string;
  price: number;
  seq: number;
  qty: number;
  diet: Dietary;
}

export type Dietary = "veg" | "nonveg" | "egg" | null | undefined;

export interface AvailableItem {
  inventoryId: number;
  sellerItemId: number;
  masterItemId: number;
  itemName: string;
  itemUrl: string;
  packaging: string;
  itemTag: string;
  unit: string;
  pricePerUnit: number;
  incrementOrderQty: number;
  minimumOrderQty: number;
  maxAvailableQty: number;
  orderedQty: number;
  orderedAmount: number;
  soldout: boolean;
  closed: boolean;
  unitWtFactor: number;
  itemCategories: number[];
  strikeoffPrice: number;
  discPerc: number;
  itemRegionalLanguageName: string;
  newlyAdded: boolean;
  freqScore: number;
  varGroup: string;
  varSeq: number;
  localItemCount?: number;
  diet: Dietary;
  aogList?: AogList[];
  description?: string;
  itemVariationList?: ItemVariationDto[];
  freeItem: boolean;
  supplier?: string;
}

export interface ItemVariationDto {
  id: number;
  name: string;
  groupName: string;
  seq: number;
  price: number;
  strikeoffPrice: number;
  qty: number;
  addOnGroupList: AogList[];
}

export interface SellerOption {
  id: number;
  buyerId: number;
  sellerId: number;
  deliveryDate: string;
  sellerName: string;
  deliveryTime: string;
  rating: number;
  codAllowed: boolean;
  orderedItemCount: number;
  buyerInServiceArea: boolean;
  totalOrderedAmount: number;
  isBookingAllowed: boolean;
  listingSequence: number;
  minOrderQty: number;
  minOrderValue: number;
  existingOrderGroupId: number;
  availableItems: AvailableItem[];
  favItemsEnabled: boolean;
}

export interface DeliveryOption {
  deliveryDate: string;
  displayDeliveryDate?: boolean;
  bookingAllowed: boolean;
  pendingOrderCount: number;
  completedOrderCount: number;
  pendingOrderItemCount: number;
  completedOrderItemCount: number;
  appSellerSellerOption: SellerOption | null;
  avSellerCount: number;
  appSellerSellerId: number;
}

export interface CartItem {
  amount: number;
  itemId: number;
  qty: number;
  cartKey: string;
  aogList?: AogList[];
  flatAddons?: AddonItem[];
  variationId?: number;
}

export interface Cart {
  [key: string]: CartItem;
}

export interface BuyerHomeData {
  deliveryOptions?: DeliveryOption[];
  pendingPaymentsCount: number;
  pendingPaymentsAmount: number;
  masterBuyerId: number;
  masterBuyerName: string;
  networkBannerUrls: string[];
  defaultAddress?: AddressDto;
}

export interface ItemCategoryDtos {
  id: number;
  name: string;
  picture: string;
  level?: number;
  parentCategories?: number[];
}

export type NetworkType = "B2B" | "B2C";

export interface ItemOptionsData {
  inventoryId: number;
  buyerId: number;
  sellerId: number;
  deliveryDate: string;
  sellerName: string;
  deliveryTime: string;
  estDeliveryTime: string;
  rating: number;
  codAllowed: boolean;
  orderedItemCount: number;
  totalOrderedAmount: number;
  isBookingAllowed: boolean;
  listingSequence: number;
  minOrderQty: number;
  minOrderValue: number;
  availableItems: AvailableItem[];
  orderedQty: number;
  existingOrderGroupId?: number;
  prepayDiscountPercentage: number;
  approxPricing: boolean;
  categoriesEnabled: boolean;
  itemCategoryDtos: ItemCategoryDtos[];
  itemCategoryL2Dtos: ItemCategoryDtos[];
  itemCategoryL3Dtos: ItemCategoryDtos[];
  favItemsEnabled: boolean;
  networkType: NetworkType;
  sellerBanners: string[];
  sellerLogo: string;
  buyerAddress: string;
  cartKey: string;
  avStatus: "ok" | "bookingClosed" | "notInServiceArea";
  selectedL1CategoryId: number;
  takeAwayEnabled: boolean;
  defaultAddress: AddressDto;
  ongoingOrder?: Order;
  displayPrices?: boolean;
  displayDeliveryDate?: boolean;
}

export interface PrecheckOrderItem {
  inventoryId: number;
  sellerId: number;
  sellerItemId: number;
  pricePerUnit: number;
  quantity: number;
  addOnItems: AddonItem[];
  variationId?: number;
}

export type FulfillmentType = "TAKE_AWAY" | "DELIVERY";

export interface PrecheckOrderPayload {
  sellerInventoryId: number;
  buyerId: number;
  deliveryDate: string;
  sellerId: number;
  codOpted: boolean;
  items: PrecheckOrderItem[];
  legacy: boolean;
  moneyCollectionId: number;
  existingOrderGroupId?: number;
  cartKey: string;
  preconfirmUid?: string;
  sellerMessage?: string;
  couponId?: number;
  fulfillmentType?: FulfillmentType;
}

export interface PrecheckOrderResponse {
  buyerId: number;
  sellerId: number;
  deliveryDate: string;
  sellerInventoryId: number;
  balancePayableAmount: number;
  paymentRequired: boolean;
  sellerName: string;
  deliveryTime: string;
  estDeliveryTime: string;
  totalOrderAmount: number;
  deliveryCharges: number;
  totalAmount: number;
  prepayDiscountPercentage: number;
  codAllowed: boolean;
  codSelected: boolean;
  existingOrderGroupId: number;
  codAmount: number;
  walletAmount: number;
  discountAmount: number;
  cartKey: string;
  minOrderQty: number;
  minOrderValue: number;
  approxPricing: boolean;
  insufficientOrderQuantity: boolean;
  insufficientOrderValue: boolean;
  sellerMessage?: string;
  items: SellerOrderItem[];
  defaultAddress: AddressDto;
  preconfirmUid: string;
  nBuyerId: number;
  discountId: number;
  totalTaxAmount: number;
  packagingCharges: number;
  platformFee: number;
  itemsTax: number;
  packagingTax: number;
  platformTax: number;
  deliveryTax: number;
  applicableCoupons: CouponDTO[];
  appliedCoupon: AppliedCouponDTO;
  couponId?: number;
  takeAwayEnabled: boolean;
  fulfillmentType: FulfillmentType;
  displayPrices?: boolean;
}

export interface ConfirmOrderPayload {
  sellerInventoryId: number;
  buyerId: number;
  codOpted: boolean;
  codAllowed: boolean;
  codAmount?: number;
  deliveryDate: string;
  deliveryCharges: number;
  discountAmount: number;
  walletAmount: number;
  existingOrderGroupId?: number;
  cartKey: string;
  sellerMessage?: string;
  items: Array<{
    sellerId: number;
    inventoryId: number;
    sellerItemId: number;
    pricePerUnit: number;
    quantity: number;
    amount: number;
  }>;
  preconfirmUid?: string;
}

export interface ConfirmOrderResponse {
  url: string;
  status: number;
  data: string;
  defaultAddress: AddressDto;
}

export interface SellerOrderItem {
  inventoryId: number;
  sellerServiceAreaId: number;
  farmerServiceAreaId: number;
  masterItemId: number;
  itemName: string;
  packaging: string;
  itemUrl: string;
  unit: string;
  sellerId: number;
  farmerId: number;
  sellerItemId: number;
  pricePerUnit: number;
  quantity: number;
  amount: number;
  avgPrice: number;
  isSoldOut: boolean;
  weightFactor: number;
  availableCartItem: AvailableItem;
  addOnItemDtos: AddonItem[];
  variationId?: number;
  strikeOffAmount?: number;
}

export interface SellerOrder {
  sellerInventoryId: number;
  sellerName: string;
  deliveryDate: string;
  deliveryTime: string;
  rating: number;
  codAllowed: boolean;
  codSelected: boolean;
  prepayDiscountPercentage: number;
  existingOrderGroupId?: number;
  moneyCollectionId: number;
  totalOrderAmount: number;
  deliveryCharges: number;
  discountAmount: number;
  totalAmount: number;
  walletAmount: number;
  balancePayableAmount: number;
  paymentRequired: boolean;
  items: SellerOrderItem[];
}

// app/types/index.ts
export interface OrderResponse {
  pendingOrders: Order[];
  completedOrders: Order[];
}

export type OrderStatus =
  | "Cancelled"
  | "Delivered"
  | "Packed"
  | "PickedUp"
  | "Dispatched"
  | "Assigned"
  | "Created"
  | "Accepted";
export type LogisticStatus =
  | "LOG_CREATED"
  | "LOG_PENDING"
  | "LOG_SEARCHING_AGENT"
  | "LOG_AGENT_ASSIGNED"
  | "LOG_AT_PICKUP"
  | "LOG_PICKED_UP"
  | "LOG_REACHED_LOCATION"
  | "LOG_DELIVERED"
  | "LOG_RTO_INITIATED"
  | "LOG_CANCELLED";
export interface Order {
  id: number;
  sellerName: string;
  sellerContactNumber: string;
  sellerEmail?: string;
  sellerGstin?: string;
  sellerPanNumber?: string;
  deliveryCode: string;
  deliveryDate: string;
  deliveryTime: string;
  estDeliveryTime: string;
  status: OrderStatus;
  totalItemCount: number;
  deliveredItemCount: number;
  cancelledItemCount: number;
  totalWeight: number;
  totalOrderAmount: number;
  deliveryCharges: number;
  codAmount: number;
  discountAmount: number;
  totalAmount: number;
  isPending: boolean;
  farmers: Farmer[];
  delayPaymentPendingAmount: number;
  sellerId: number;
  paidCodAmount: number;
  paidOnlineAndDirectAmount: number;
  creditPaidAmount: number;
  createdOn: string;
  buyerName: string;
  buyerAddress: string;
  buyerContactNumber: string;
  buyerEmail?: string;
  buyerGstin?: string;
  delPartnerName: string;
  delPartnerNumber: string;
  logStatus: LogisticStatus;
  walletAmount: number;
  cashPaid: number;
  balanceTobePaid: number;
  totalTaxAmount: number;
  packagingCharges: number;
  platformFee: number;
  fulfillmentType: FulfillmentType;
  itemsTax: number;
  packagingTax: number;
  platformTax: number;
  deliveryTax: number;
  displayPrices?: boolean;
}

export interface Farmer {
  farmerId: number;
  farmerName: string;
  farmerRating: number;
  items: OrderItem[];
  sellerContactNumber: string;
  sellerAddress: string;
}

export interface OrderItem {
  orderId: number;
  itemName: string;
  packaging: string;
  itemUrl: string;
  itemRegionalLanguageName: string;
  qty: number;
  price: number;
  amount: number;
  status: string;
  unit: string;
  cancelledQty: number;
  returnedQty: number;
  diet: Dietary;
  addOns: AogList[];
  itemTaxAmount: number;
  strikeOffAmount?: number;
  freeItem: boolean;
  supplier?: string;
}

export interface InitiatePayment {
  initiatedByUserId: number;
  amount: number;
  note: string;
}
export interface InitiatePaymentResponse {
  refId: number;
  queryString: string;
  pa: string;
  pn: string;
  mc: string;
  tr: string;
  tn: string;
  am: string;
  cu: string;
  status: "PAID" | "FAILED" | "PENDING" | "INITIATED";
  ogid: number;
  merchantId: string;
  message: string;
}

export type PaymentGateway = "icici_upi" | "rp";

export interface RPPaymentDetails {
  mcId: number; //moneyCollectionId
  pgRefId: string; //pg orderId or reference id
  amount: number;
  status: string; //payment status
  currency: string;
  razorPayKey: string;
}
export interface MoneyCollectionDetails {
  paymentGateway: PaymentGateway;
  iciciUpiPaymentDetails: InitiatePaymentResponse;
  rpPaymentDetails: RPPaymentDetails;
  id: number;
  status: string;
  amount: number;
}

export interface UpiPaymentStatusDetails {
  moneyCollectionDetails: MoneyCollectionDetails;
  paymentPending: boolean;
  paymentSuccess: boolean;
  postActionPending: boolean;
  ogId: number;
  amountCollected: number;
  nBuyerWalletBalance: number;
}

export type RouteNames =
  | "home"
  | "chooseitems"
  | "orders"
  | "wallet"
  | "selectSeller"
  | "account"
  | "help";

export type AppDomain = "RET11" | "RET10";

export interface NetworkConfig {
  domain: string;
  businessLogo: string;
  homePageBanner: string;
  pwaAppIcon: string;
  footerAppIcon: string;
  networkId: number;
  multiSeller: boolean;
  defaultStartPage: RouteNames;
  wabMobileNumber: string;
  ondcDomain: AppDomain;
  networkType: NetworkType;
  wabEnabled: boolean;
  wabDatasetId: string;
  pwaAppName?: string;
  pwaAppShortName?: string;
  pwaAppDiscription?: string;
  pwaAppIconSize?: string;
  pwaAppIconType?: string;
  enableInstallPwa?: boolean;
  sellerName: string;
}

export interface ActionData<T> {
  success?: boolean;
  error?: string;
  data?: T;
}
export interface AppInfo {
  version?: number;
  packageName?: string;
  versionName: string;
}

export type ImageViewType = "GRID" | "GALLERY" | "LIST" | "R-VIEW" | null;

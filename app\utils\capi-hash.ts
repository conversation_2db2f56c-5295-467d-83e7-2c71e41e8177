// Conversion API data hashing utilities
// Handles SHA-256 hashing of user data as required

/**
 * Hash a string using SHA-256 (browser environment)
 * @param data - String to hash
 * @returns Promise resolving to hex-encoded hash
 */
export async function hashSHA256Browser(data: string): Promise<string> {
  if (typeof window === "undefined" || !window.crypto?.subtle) {
    throw new Error("Web Crypto API not available");
  }

  try {
    // Normalize the data (lowercase, trim whitespace)
    const normalizedData = data.toLowerCase().trim();
    
    // Encode the string as UTF-8
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(normalizedData);
    
    // Hash the data
    const hashBuffer = await window.crypto.subtle.digest("SHA-256", dataBuffer);
    
    // Convert to hex string
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, "0")).join("");
    
    return hashHex;
  } catch (error) {
    console.error("Error hashing data:", error);
    throw error;
  }
}

/**
 * Hash a string using SHA-256 (Node.js environment)
 * @param data - String to hash
 * @returns Hex-encoded hash
 */
export function hashSHA256Node(data: string): string {
  try {
    const crypto = require("crypto");
    
    // Normalize the data (lowercase, trim whitespace)
    const normalizedData = data.toLowerCase().trim();
    
    // Create hash
    const hash = crypto.createHash("sha256");
    hash.update(normalizedData, "utf8");
    
    return hash.digest("hex");
  } catch (error) {
    console.error("Error hashing data in Node.js:", error);
    throw error;
  }
}

/**
 * Hash user data for Conversion API
 * Automatically detects environment and uses appropriate hashing method
 * @param data - String to hash
 * @returns Promise resolving to hex-encoded hash
 */
export async function hashUserData(data: string): Promise<string> {
  if (!data || data.trim() === "") {
    throw new Error("Data to hash cannot be empty");
  }

  // Check if we're in a browser environment
  if (typeof window !== "undefined") {
    return await hashSHA256Browser(data);
  } else {
    // Node.js environment
    return hashSHA256Node(data);
  }
}

/**
 * Hash phone number for Conversion API
 * Removes common formatting and normalizes before hashing
 * @param phoneNumber - Phone number to hash
 * @returns Promise resolving to hashed phone number
 */
export async function hashPhoneNumber(phoneNumber: string): Promise<string> {
  if (!phoneNumber || phoneNumber.trim() === "") {
    throw new Error("Phone number cannot be empty");
  }

  try {
    // Remove all non-digit characters
    let normalized = phoneNumber.replace(/\D/g, "");
    
    // Add country code if not present (assuming +91 for India)
    if (!normalized.startsWith("91") && normalized.length === 10) {
      normalized = "91" + normalized;
    }
    
    return await hashUserData(normalized);
  } catch (error) {
    console.error("Error hashing phone number:", error);
    throw error;
  }
}

/**
 * Hash email address for Conversion API
 * Normalizes email before hashing
 * @param email - Email address to hash
 * @returns Promise resolving to hashed email
 */
export async function hashEmail(email: string): Promise<string> {
  if (!email || email.trim() === "") {
    throw new Error("Email cannot be empty");
  }

  try {
    // Normalize email (lowercase, trim)
    const normalized = email.toLowerCase().trim();
    
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(normalized)) {
      throw new Error("Invalid email format");
    }
    
    return await hashUserData(normalized);
  } catch (error) {
    console.error("Error hashing email:", error);
    throw error;
  }
}

/**
 * Hash name for Conversion API
 * Normalizes name before hashing
 * @param name - Name to hash
 * @returns Promise resolving to hashed name
 */
export async function hashName(name: string): Promise<string> {
  if (!name || name.trim() === "") {
    throw new Error("Name cannot be empty");
  }

  try {
    // Normalize name (lowercase, trim, remove extra spaces)
    const normalized = name.toLowerCase().trim().replace(/\s+/g, " ");
    
    return await hashUserData(normalized);
  } catch (error) {
    console.error("Error hashing name:", error);
    throw error;
  }
}

/**
 * Hash location data for Conversion API
 * @param location - Location string to hash
 * @returns Promise resolving to hashed location
 */
export async function hashLocation(location: string): Promise<string> {
  if (!location || location.trim() === "") {
    throw new Error("Location cannot be empty");
  }

  try {
    // Normalize location (lowercase, trim)
    const normalized = location.toLowerCase().trim();
    
    return await hashUserData(normalized);
  } catch (error) {
    console.error("Error hashing location:", error);
    throw error;
  }
}

/**
 * Batch hash multiple user data fields
 * @param userData - Object containing user data to hash
 * @returns Promise resolving to object with hashed values
 */
export async function hashUserDataBatch(userData: {
  phoneNumber?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
}): Promise<{
  ph?: string;
  em?: string;
  fn?: string;
  ln?: string;
  ct?: string;
  st?: string;
  zp?: string;
  country?: string;
}> {
  const hashedData: any = {};

  try {
    // Hash phone number
    if (userData.phoneNumber) {
      hashedData.ph = await hashPhoneNumber(userData.phoneNumber);
    }

    // Hash email
    if (userData.email) {
      hashedData.em = await hashEmail(userData.email);
    }

    // Hash first name
    if (userData.firstName) {
      hashedData.fn = await hashName(userData.firstName);
    }

    // Hash last name
    if (userData.lastName) {
      hashedData.ln = await hashName(userData.lastName);
    }

    // Hash city
    if (userData.city) {
      hashedData.ct = await hashLocation(userData.city);
    }

    // Hash state
    if (userData.state) {
      hashedData.st = await hashLocation(userData.state);
    }

    // Hash zip code
    if (userData.zipCode) {
      hashedData.zp = await hashUserData(userData.zipCode);
    }

    // Hash country
    if (userData.country) {
      hashedData.country = await hashLocation(userData.country);
    }

    return hashedData;
  } catch (error) {
    console.error("Error batch hashing user data:", error);
    throw error;
  }
}

/**
 * Validate that a string is a valid SHA-256 hash
 * @param hash - Hash string to validate
 * @returns true if valid SHA-256 hash, false otherwise
 */
export function isValidSHA256Hash(hash: string): boolean {
  if (!hash || typeof hash !== "string") {
    return false;
  }

  // SHA-256 hash should be 64 characters long and contain only hex characters
  const sha256Regex = /^[a-f0-9]{64}$/i;
  return sha256Regex.test(hash);
}

/**
 * Synchronous hashing for server-side use only
 * @param data - Data to hash
 * @returns Hashed data
 */
export function hashUserDataSync(data: string): string {
  if (typeof window !== "undefined") {
    throw new Error("Synchronous hashing not available in browser environment");
  }
  
  return hashSHA256Node(data);
}

/**
 * Hash phone number synchronously (server-side only)
 * @param phoneNumber - Phone number to hash
 * @returns Hashed phone number
 */
export function hashPhoneNumberSync(phoneNumber: string): string {
  if (typeof window !== "undefined") {
    throw new Error("Synchronous hashing not available in browser environment");
  }

  if (!phoneNumber || phoneNumber.trim() === "") {
    throw new Error("Phone number cannot be empty");
  }

  // Remove all non-digit characters
  let normalized = phoneNumber.replace(/\D/g, "");
  
  // Add country code if not present (assuming +91 for India)
  if (!normalized.startsWith("91") && normalized.length === 10) {
    normalized = "91" + normalized;
  }
  
  return hashUserDataSync(normalized);
}

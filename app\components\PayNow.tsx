import { useState, useCallback, useEffect } from "react";
import { usePayment } from "~/hooks/usePayment";
import { PaymentInitiateRequest } from "~/types/payment.types";
import { PaymentModal } from "./PaymentModal";
import { Wallet } from "lucide-react";
import Toast from "./Toast";
import <PERSON><PERSON>payHandler from "./RazorpayHandler";
import { useAppConfigStore } from "~/stores/appConfig.store";
import { useConversionApi } from "~/hooks/useConversionApi";

interface PayNowProps {
  paymentRequest: PaymentInitiateRequest;
  buttonClassName?: string;
  iconClassName?: string;
  textClassName?: string;
  buttonText?: string;
  onSuccess?: () => void;
  onClose?: () => void;
  retryCallback?: () => void;
  useDeposit?: boolean;
  preconfirmUid?: string;
  disabled?: boolean;
  customerInfo?: {
    name?: string;
    email?: string;
    contact?: string;
  };
}

/**
 * A reusable PayNow component that can be used across the application
 * to initiate payments.
 */
export const PayNow = ({
  paymentRequest,
  buttonClassName = "bg-teal-600 text-white px-4 py-2 rounded-lg text-sm font-medium flex items-center",
  iconClassName = "mr-2",
  textClassName = "",
  buttonText = "Pay now",
  onSuccess,
  onClose,
  retryCallback,
  useDeposit = false,
  preconfirmUid,
  disabled = false,
  customerInfo = {}
}: PayNowProps) => {
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState<"success" | "error">("error");
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  const { networkConfig } = useAppConfigStore();
  const { trackInitiateCheckout, trackPurchase } = useConversionApi();

  // Toast handler function
  const handleToast = useCallback(
    (message: string, type: "success" | "error") => {
      setToastMessage(message);
      setToastType(type);
      setShowToast(true);
    },
    []
  );

  const payment = usePayment({
    onToast: handleToast
  });

  // Handle payment initiation
  const handlePayClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    // Return early if the button is disabled
    if (disabled) {
      return;
    }

    trackInitiateCheckout({
      eventData: {
        currency: "INR",
        value: paymentRequest.amount,
        contentIds: [ paymentRequest.orderGroupId?.toString() || "" ],
      },
      eventId: `EVT-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      eventTime: Date.now()
    });

    if (useDeposit) {
      // Validate preconfirmUid for deposit flow
      if (!preconfirmUid) {
        console.error("Missing preconfirmUid for deposit payment");
        handleToast(
          "Unable to process payment: Missing order information",
          "error"
        );
        return;
      }

      if (
        !paymentRequest?.amount ||
        typeof paymentRequest.amount !== "number"
      ) {
        console.error("Invalid payment amount:", paymentRequest?.amount);
        handleToast("Unable to process payment: Invalid amount", "error");
        return;
      }

      try {
        payment.handleDeposit(preconfirmUid, paymentRequest.amount);
        setShowPaymentModal(true);
      } catch (error) {
        console.error("Error processing deposit request:", error);
        handleToast("Unable to process payment. Please try again.", "error");
      }
    } else {
      // Standard payment flow
      if (!paymentRequest || typeof paymentRequest !== "object") {
        console.error("Invalid payment request:", paymentRequest);
        handleToast(
          "Unable to process payment. Invalid payment data.",
          "error"
        );
        return;
      }

      try {
        payment.handlePayNow(paymentRequest);
        setShowPaymentModal(true);
      } catch (error) {
        console.error("Error processing payment request:", error);
        handleToast(
          "Unable to process payment. Invalid payment data.",
          "error"
        );
      }
    }
  };

  // Handle payment modal close
  const handleModalClose = useCallback(
    (e?: React.MouseEvent) => {
      if (e) {
        e.preventDefault();
        e.stopPropagation();
      }

      payment.handleClose();
      setShowPaymentModal(false);
      setShowToast(false); // Hide toast when modal closes

      if (onClose) {
        onClose();
      }
    },
    [payment, onClose]
  );

  // Handle payment success
  const handlePaymentSuccess = useCallback(() => {
    if (onSuccess) {
      onSuccess();
    }
    handleModalClose();
    trackPurchase({
      eventData: {
        currency: "INR",
        value: paymentRequest.amount,
        contentIds: [ paymentRequest.orderGroupId?.toString() || "" ],
      },
      eventId: `EVT-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`,
      eventTime: Date.now()
    });
  }, [onSuccess, handleModalClose]);

  // Handle toast close
  const handleToastClose = useCallback(() => {
    setShowToast(false);
  }, []);

  // Add effect to monitor payment status
  useEffect(() => {
    console.log("PayNow - Payment status:", payment.status);
  }, [payment.status]);

  // Only show if there's a payment amount
  if (!paymentRequest.amount || paymentRequest.amount <= 0) {
    return null;
  }

  return (
    <>
      <button
        onClick={(e) => {
          e.stopPropagation();
          handlePayClick(e);
        }}
        className={buttonClassName}
        disabled={payment.isProcessing || disabled}
      >
        <Wallet size={20} className={iconClassName} />
        <span className={textClassName}>
          {payment.isProcessing ? "Processing..." : buttonText}
        </span>
      </button>

      {showPaymentModal && (
        <PaymentModal
          isOpen={payment.isOpen}
          isLoading={payment.isLoading}
          status={payment.status}
          message={payment.message}
          handleClose={handleModalClose}
          onSuccess={handlePaymentSuccess}
          retryCallback={retryCallback}
          refId={payment.refId}
        />
      )}

      {/* Razorpay Integration */}
      {payment.showRazorpay && payment.razorpayOptions && (
        <RazorpayHandler
          rpPaymentDetails={payment.razorpayOptions}
          customerInfo={customerInfo}
          onSuccess={payment.handleRazorpaySuccess}
          onFailure={payment.handleRazorpayFailure}
          onClose={payment.handleRazorpayClose}
          companyName={networkConfig?.sellerName || "mNet"}
        />
      )}

      {/* Toast for errors */}
      {showToast && (
        <Toast
          type={toastType}
          message={toastMessage}
          onClose={handleToastClose}
          duration={3000}
          position="bottom-center"
          width="full"
        />
      )}
    </>
  );
};

export default PayNow;
